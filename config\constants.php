<?php
    return [
        'sites_name' => [
            1=>"お知らせ",
            2=>"メディア掲載",
            3=>"ニュースリリース"
        ],

        'action_filter' => [
            '電話',
            '受注',
            '訪問営業',
            '派遣業確',
            '紹介業確',
            '派遣開始',
            '紹介開始',
            '派遣売上',
            '紹介売上'
        ],

    'payment' => [
        'eme_food'          => '非常食',
        'gift'              => 'ギフト',
        'foodstuff'         => '食材',
        'material'          => '資材',
        'rep_traffic'       => '立替交通',
        'rep_material'      => '立替資材',
        'rep_ingredient'    => '立替食材',
        'post'              => '郵便',
        'recruit_fee'       => '募集費',
        'sma_ingredient'    => '小口食材',
        'sma_material'      => '小口資材'
    ],

    'salary' => [
        'labor_cost'        => '社員人件費',
        'person_expense'    => 'パート人件費',
        'com_insu_paid'     => '社保(個人負担)',
        'com_insu_burden'   => '社保(会社負担)',
        'meal_bill'         => '食事代',
        'paid_holiday'      => '有休',
        'medical_bill'      => '医療費',
        'communi_fee'       => '通信費',
        'trans_expense'     => '交通費',
        'dependent_allow'   => '扶養手当'
    ],

    'insurance' => [
        'emp_labor_cost'           =>  '社員人件費（ヘルプ）',
        'part_labor_cost'          =>  'パート人件費（ヘルプ）',
        'man_labor_cost'           =>  'マネージャー人件費',
        'soc_insu_person_paid'     =>  '社保ヘルプ(個人負担）',
        'soc_insu_company_burden'  =>  '社保ヘルプ(会社負担)'
    ],

    'system' => [
        'commission'            =>  '委託費',
        'food_cost'             =>  '食材費',
        'purcharse'             =>  '個人購入費',
        'other'                 =>  'その他',
        'parking'               =>  '駐車場代',
        'asmo_care_cost'        =>  'アスモ介護食材費',
        'num_meal'              =>  '食数',
        'num_meal_day'          =>  '食数デイ',
    ],

    'payment_cate'          => 1,
    'salary_cate'           => 2,
    'insurance_cate'        => 3,
    'system_cate'           => 4,
    'max_sheet'             => 100,
    'max_chunk'             => 100,
    'max_rows'             => 5000,
    'chunk_overall'        => 20,
    'chunk_print'          => 50,
    'chunk_ajax'        => 10,
    'perpage'           => 20,
    'width_page_a4'         => '210',
    'width_page_b4'         => '250',
    'height_page_a4'        => '297',
    'height_page_b4'        => '353',
    'width_a4'              => 939,
    'width_b4'              => 1120,
    'height_a4'             => 1355,
    'height_b4'             => 1640,
    'height_toolbar'        => 80, // Chiều cao toolbar tinymce
    'margin_left'           => 0,
    # for textbox - start
    'width_textbox'         => 200, // Chiều dài mặc định textbox [textbox stamp]
    'fontsize_textbox'      => 20, // Fontsize mặc định textbox [textbox stamp]
    'endPointExportPDF'     => env('END_POINT_EXPORT_PDF',NULL),
    # for textbox - end
    # Real path of control site when access control from portal page - start
    'ctrl_public_path'      => env('CTRL_PUBLIC_PATH',NULL),
    'ctrl_storage_path'     => env('CTRL_PUBLIC_STORAGE_PATH',NULL),
    'ctrl_url'              => env('CTRL_URL',NULL),
    # Real path of control site when access control from portal page - start

    'passPrefix'            => '001e063737d03cfde999524c891f583687591d19', 
    'userTypeAdmin'         => 1, 
    'userTypeStaff'         => 2, 
    'userTypeCompany'       => 3, 

    'data_survey' => [
        [
            "TYPE" => "介護, 看護助手, 看護, 保育, 幼稚園教諭, 児童指導員, 保育補助, 調理, 栄養士, 管理栄養士, 調理補助, DEFAULT",
            "Q" => "あなたについて少し教えてください。どちらかと言うと…",
            "ANS" => [
                "明るい性格だ",
                "普通かな",
                "体力には自信がある",
                "体力には自信がない",
                "自分の意見はハッキリ言う方だ",
                "周りに合わせる方だ",
                "人間関係が得意",
                "人間関係が苦手",
                "仕事の要領は良い方だ",
                "仕事の要領は悪い方だ",
            ]
        ],
        [
            "TYPE" => "介護, 看護助手, 看護, 保育, 幼稚園教諭, 児童指導員, 保育補助, 調理, 栄養士, 管理栄養士, 調理補助, DEFAULT",
            "Q" => "あなたの働き方のご希望について聞かせてください。どちらかと言うと…",
            "ANS" => [
                "色々な職場を経験したい",
                "一か所で長く働きたい",
                "資格をUPしていきたい",
                "資格はUPは考えていない",
                "単発勤務に興味がある",
                "単発勤務には興味なし",
            ]
        ],
        [
            "TYPE" => "介護, 看護助手, 看護, 保育, 幼稚園教諭, 児童指導員, 保育補助, 調理, 栄養士, 管理栄養士, 調理補助, DEFAULT",
            "Q" => "転職に対し優先順位をお聞かせください。どちらかと言うと…",
            "ANS" => [
                "給与優先",
                "職場環境優先",
                "給与 優先",
                "通勤距離優先",
            ]
        ],
        [
            "TYPE" => "介護, 看護助手",
            "Q" => "介護のお仕事を希望するきっかけをお聞かせください。",
            "ANS" => [
                "高齢者が好きだから",
                "親族の介護をきっかけに",
                "身の回りに医療従事者がいたから",
                "その他",
            ]
        ],
        [
            "TYPE" => "介護, 看護助手, 看護",
            "Q" => "働くなら、どちらかと言うと…",
            "ANS" => [
                "忙しい職場",
                "ゆったりした職場",
                "介護度が高い職場",
                "介護度が低い職場",
                "土日勤務多め",
                "土日勤務少なめ",
            ]
        ],
        [
            "TYPE" => "看護",
            "Q" => "ご経験についてお聞かせください",
            "ANS" => [
                "病院経験あり",
                "病院経験なし",
                "高齢者福祉施設での経験あり",
                "高齢者福祉施設での経験なし",
                "障害者施設での経験あり",
                "障害者施設での経験なし",
            ]
        ],
        [
            "TYPE" => "保育, 幼稚園教諭, 児童指導員, 保育補助",
            "Q" => "保育士資格取得は考えておりますか？どちらかと言うと…",
            "ANS" => [
                "視野に入れている",
                "取得は考えていない",
            ],
            "TITLE" => "保育士資格をお持ちでない方にお聞きします。"
        ],
        [
            "TYPE" => "調理, 栄養士, 管理栄養士, 調理補助",
            "Q" => "ご経験はありますか？",
            "ANS" => [
                "集団調理での経験あり",
                "集団調理での経験なし",
                "飲食店での調理経験あり",
                "飲食店での調理経験なし",
            ],
            "TITLE" => "調理のお仕事経験についてお聞かせください。"
        ],
    ],

    'reflect_result' => [
        '明るい性格だ' => [
            'career' => '性格は明るい方です',
            'company' => [
                '場の雰囲気を明るくしてくれる方です',
                'スタッフの方々、利用者さんと良好な関係を築ける方です',
                '盛り上げ上手な性格を活かして明るく働ける方です',
                '職場の雰囲気を明るくしてくれる方です！',
                '職場の雰囲気を良くします！',
                '場の雰囲気を明るくしてくれる方です',
                'スタッフの方々、利用者さんと良好な関係を築ける方です',
                '盛り上げ上手な性格を活かして明るく働ける方です',
            ],
            'staff' => [
                '職場環境にすぐ馴染めることが自慢です',
                'コミュニケーション能力を活かして活躍します'
            ]
        ],
        '普通かな' => [
            'career' => '',
            'company' => [

            ],
            'staff' => [
                'もくもくとしっかり業務をおこなします。',
            ]
        ],
        '体力には自信がある' => [
            'career' => '体力に自信あります',
            'company' => [
                '男性や身体の大きい利用者に困ってませんか？'
            ],
            'staff' => [
                '力仕事はお任せください！',
                'トランスなど力が必要な仕事も得意です',
                'トランスや移乗などの力仕事が得意です！',
            ]
        ],
        '周りに合わせる方だ' => [
            'career' => '協調性があります',
            'company' => [
                'スタッフ間の連携を意識してお仕事できる方です'
            ],
            'staff' => [
                '穏やかな性格を活かしてより良いケアを実現します',
                'チームワークを大切に業務に取り組みます他の介護士さんと円滑に仕事が出来ます！',
            ]
        ],
        '人間関係が得意' => [
            'career' => '',
            'company' => [
                'スタッフ間の連携を意識して円滑に働ける方です'
            ],
            'staff' => [
                '相手の立場で考えることを大切にしています',
                '人と接するのが好きな性格を仕事に活かします',
                '施設ごとの特性に合わせて働きます',
            ]
        ],
        '人間関係が苦手' => [
            'career' => '',
            'company' => [

            ],
            'staff' => [
                'コツコツ仕事に取り組みます',
            ]
        ],
        '仕事の要領は良い方だ' => [
            'career' => '即戦力で動けます',
            'company' => [
                '過去の就業先でも高評価をいただきました'
            ],
            'staff' => [
                'いち早く職場に慣れて能動的に働けます',
                '効率の良い働き方を実践します',
                '持ち前の適応能力で素早く業務を覚えます',
                '人の意見をきちんと受けとめて受け入れることが出来ます！',
                '施設のやり方に合わせて仕事します！',
            ]
        ],
        '仕事の要領は悪い方だ' => [
            'career' => '丁寧な仕事を心がけています',
            'company' => [
            ],
            'staff' => [
                '利用者さん一人一人の時間を大切にします！',
            ]
        ],
        '忙しい職場' => [
            'career' => '忙しい職場が希望です',
            'company' => [
                '柔軟に仕事をこなす器用な方です'
            ],
            'staff' => [
                '業務量の多い環境でも臨機応変に対応できます',
                'マルチタスク能力が自慢です',
                'バリバリ働きます！！',
            ]
        ],
        'ゆったりした職場' => [
            'career' => '',
            'company' => [
                'ひとつひとつの仕事に対して丁寧に取り組む方です'
            ],
            'staff' => [
                'ミスの無い業務を徹底します',
                '利用者さんに誠心誠意接して良好な関係を築きます',
                '利用者さんのケアを丁寧に行います！',
            ]
        ],
        '介護度が低い職場' => [
            'career' => 'コミュニケーションを重視してます',
            'company' => [
            ],
            'staff' => [
                '利用者さんに寄り添ったケアが得意です',
                '利用者さんの笑顔を見ることが何よりも喜びです',
                '一人ひとりに合わせた個別の対応を心がけます',
                '利用者の気持ちに寄り添うことを大事にしてます！',
            ]
        ],
        '土日勤務多め' => [
            'career' => '土日勤務多めにできます',
            'company' => [
                '週末のスタッフ不足にお困りではありませんか？',
                '土日の人手不足を解消しませんか',
                '人が少ないところに是非！',
                'シフト作成に強力な方です！',
            ],
            'staff' => [
                '柔軟にシフトのご相談に応じます',
            ]
        ],
        '色々な職場を経験したい' => [
            'career' => '',
            'company' => [
                '見解やスキルを高める意欲をお持ちの方です',
                '期間限定の募集にも対応させていただきます',
            ],
            'staff' => [

            ]
        ],
        '一か所で長く働きたい' => [
            'career' => '',
            'company' => [
                '長期的な勤務で安定してお役に立てます',
                '離職率にお悩みではありませんか？'
            ],
            'staff' => [
                '一か所で長く働くことが多かったです。'
            ]
        ],
        '資格をUPしていきたい' => [
            'career' => '',
            'company' => [
                'スキルアップを目指す意欲的な方です',
                'シャレンジ誠心旺盛で仕事に熱心な方です',
                '介護業界に長く携わりたい意識が高い方です！',
            ],
            'staff' => [
                '仕事に対する情熱は誰にも負けません'
            ]
        ],
        '単発勤務に興味がある' => [
            'career' => '単発勤務興味あります',
            'company' => [
                '急な人材不足にお困りではありませんか？',
                'ご紹介からお仕事開始まで迅速に対応できます',
                '状況により面談無しで即日開始もＯＫ',
                '勤務日数「1日」からご相談可能です',
            ],
            'staff' => [
                '即戦力になります！'
            ]
        ],
        '高齢者が好きだから' => [
            'career' => '',
            'company' => [

            ],
            'staff' => [
                '高齢者が大好きでこの仕事を希望します'
            ]
        ],
        '親族の介護をきっかけに' => [
            'career' => '',
            'company' => [

            ],
            'staff' => [
                '家庭内で介護に携わってきました！'
            ]
        ],
        '全て選択した人早番～夜勤' => [
            'career' => 'フルタイムです',
            'company' => [
                '早番～夜勤までのフルタイムシフト対応可能です。'
            ],
            'staff' => [
                '全てのシフト対応可能です！！'
            ]
        ],
        '早番・日勤・遅番' => [
            'career' => '日勤隊シフト対応可能です',
            'company' => [
                '早番～遅番までシフト対応可能です！'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '早番' => [
            'career' => '',
            'company' => [
                '早番'
            ],
            'staff' => [
                '早番勤務はお任せください！！'
            ]
        ],
        '日勤' => [
            'career' => '',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '遅番' => [
            'career' => '',
            'company' => [
                '遅番の職員さんは足りておりますか？'
            ],
            'staff' => [
                '遅番勤務はお任せください！！'
            ]
        ],
        '夜勤' => [
            'career' => '',
            'company' => [
                '夜勤職員は足りておりますか？',
                '夜勤シフトに是非ご協力させてください！！',
            ],
            'staff' => [
                '夜勤勤務はお任せください！！'
            ]
        ],
        '時間が8時間以下の場合' => [
            'career' => '',
            'company' => [
                '短時間勤務を希望されております。入浴専門などスポット的な使い方はいかがでしょうか。'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '派遣・予定派遣・紹介' => [
            'career' => '',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '派遣・予定派遣' => [
            'career' => '正社員・バートも検討してます',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '予定派遣・紹介' => [
            'career' => '',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '派遣のみ' => [
            'career' => '',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '予定派遣のみ' => [
            'career' => '正社員・バート検討してます',
            'company' => [
                'ゆくゆくは直接雇用での勤務を検討されている方です。是非お試しください！！'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '紹介のみ' => [
            'career' => '正社員・バート希望です。',
            'company' => [
                '直接雇用の転職を希望されている方です。是非ご検討ください。'
            ],
            'staff' => [
                '長く働ける職場を探してます！！'
            ]
        ],
        '介護業務の手伝いは苦にならない' => [
            'career' => '介護業務お手伝い苦になりません',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '介護業務の手伝いはしたくない' => [
            'career' => '',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '高齢者施設で働く場合、医療依存度の高い施設希望' => [
            'career' => '医療依存度の高い施設希望',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '高齢者施設で働く場合、医療依存度の低い施設希望' => [
            'career' => '',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '病院経験あり' => [
            'career' => '病院経験あります',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '病院経験なし' => [
            'career' => '',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '高齢者福祉施設経験あり' => [
            'career' => '高齢者施設経験あります',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '高齢者福祉施設経験なし' => [
            'career' => '',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '障害者施設での経験あり' => [
            'career' => '障害者施設での経験あります',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '障害者施設での経験なし' => [
            'career' => '',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '視野に入れている' => [
            'career' => '保育士資を格取得したいと考えております',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '所得は考えていない' => [
            'career' => '',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '集団調理の経験あり' => [
            'career' => '集団調理の経験あります',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '集団調理の経験なし' => [
            'career' => '',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '飲食店での勤務経験あり' => [
            'career' => '飲食店での料理経験あります',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
        '飲食店での勤務経験なし' => [
            'career' => '',
            'company' => [
                'コメント'
            ],
            'staff' => [
                'コメント'
            ]
        ],
    ],
    'state_default' => 1 ,
    'MAIL_INFO_SYSTEM' => env('MAIL_INFO_SYSTEM', '<EMAIL>'),
    'EDIT_ADMIN' => [
        'INSERT' => '新規',
        'UPDATE' => '更新',
        'DELETE' => '削除',
        'NOTWORK' => '更新しない',
    ],

    'upload_destination' => [
        '1_本社' => false,
        '2_西日本統括' => false,
        '3_札幌営業所' => true,
        '4_仙台営業所' => true,
        '5_名古屋営業所' => true,
        '6_大阪営業所' => true,
        '7_岡山営業所' => true,
        '8_福岡営業所' => true,
        '9_業務推進課' => false,
        '10_有給休暇管理リスト' => false,
        '11_見積入力表' => false,
        '12_イベント食【参考資料】' => false,
    ],

    'upload_destination_2' => [
        '月次(勘定用)' => '月次(勘定用)',
        '立替金精算' => '立替金精算',
        '備品購入管理表' => '備品購入管理表',
        'インディード掲載一覧(毎週月曜18時〆切)' => 'インディード掲載一覧(毎週月曜18時〆切)',
        '健康診断チェック表' => '健康診断チェック表',
    ],
    //Tax management

    //Roles
    "ROLE" => [
        'my-page' => [
            'name' => 'トップページ',
            'alias' => 'my-page',
            'id' => 1,
            'parrent_id' => 0
        ],
        'staff-search' => [
            'name' => '派遣スタッフ管理',
            'alias' => 'staff-search',
            'id' => 2,
            'parrent_id' => 0
        ],
        // 'staff-search-2' => [
        //     'name' => 'スタッフ管理',
        //     'alias' => 'staff-search-2',
        //     'id' => 3,
        //     'parrent_id' => 0
        // ],
        'time-management' => [
            'name' => 'タイムシート',
            'alias' => 'time-management',
            'id' => 3,
            'parrent_id' => 0
        ],
        'field-staff-search' => [
            'name' => 'スタッフ管理',
            'alias' => 'field-staff-search',
            'id' => 60,
            'parrent_id' => 0
        ],
        'view-fieldstaffsearch-btn' => [
            'name' => '初期設定ができる',
            'alias' => 'view-fieldstaffsearch-btn',
            'id' => 61,
            'parrent_id' => 60
        ],
        'fieldstaffsearch-view-profile-access' => [
            'name' => '個人情報が開ける',
            'alias' => 'fieldstaffsearch-view-profile-access',
            'id' => 62,
            'parrent_id' => 60
        ],
        'fieldstaffsearch-edit-profile' => [
            'name' => '個人情報変更許可をだせる',
            'alias' => 'fieldstaffsearch-edit-profile',
            'id' => 63,
            'parrent_id' => 60
        ],
        'show-approve-user-btn' => [
            'name' => '初期設定ができる',
            'alias' => 'show-approve-user-btn',
            'id' => 59,
            'parrent_id' => 3
        ],
        'list-of-contracts' => [
            'name' => '契約書',
            'alias' => 'list-of-contracts',
            'id' => 4,
            'parrent_id' => 0
        ],
        'show-bank-group-btn' => [
            'name' => '初期設定ができる',
            'alias' => 'show-bank-group-btn',
            'id' => 58,
            'parrent_id' => 4
        ],
        // 'list-of-contracts-2' => [
        //     'name' => '契約一覧',
        //     'alias' => 'list-of-contracts-2',
        //     'id' => 6,
        //     'parrent_id' => 0
        // ],
        // 'insurance-list' => [
        //     'name' => '保険一覧（派遣)',
        //     'alias' => 'insurance-list',
        //     'id' => 7,
        //     'parrent_id' => 0
        // ],
        'insurance-list' => [
            'name' => '保険一覧',
            'alias' => 'insurance-list',
            'id' => 5,
            'parrent_id' => 0
        ],

        'business-search' => [
            'name' => 'クライアント管理',
            'alias' => 'business-search',
            'id' => 6,
            'parrent_id' => 0
        ],

        'business-search-init-setting' => [
            'name' => '初期設定ができる',
            'alias' => 'business-search-init-setting',
            'id' => 64,
            'parrent_id' => 6
        ],
        //no509 start
        // 'business-search-download-csv' => [
        //     'name' => 'CSVダウンロード',
        //     'alias' => 'business-search-download-csv',
        //     'id' => 95,
        //     'parrent_id' => 6
        // ],
        //no509 end
        'billing-search' => [
            'name' => '請求情報',
            'alias' => 'billing-search',
            'id' => 7,
            'parrent_id' => 0
        ],
        // 'billing-search-2' => [
        //     'name' => '請求機能',
        //     'alias' => 'billing-search-2',
        //     'id' => 11,
        //     'parrent_id' => 0
        // ],
        'reports' => [
            'name' => '日週月報告(TG)',
            'alias' => 'reports',
            'id' => 8,
            'parrent_id' => 0
        ],

        'new-reports' => [
            'name' => '日週月報告',
            'alias' => 'new-reports',
            'id' => 85,
            'parrent_id' => 0
        ],

        'new-report-label-setting' => [
            'name' => '初期設定ができる',
            'alias' => 'new-report-label-setting',
            'id' => 86,
            'parrent_id' => 85
        ],

        'new-setup-plan' => [
            'name' => '目標設定ができる',
            'alias' => 'new-setup-plan',
            'id' => 87,
            'parrent_id' => 85
        ],

        'document-approval-status' => [
            'name' => 'ワークフロー',
            'alias' => 'document-approval-status',
            'id' => 9,
            'parrent_id' => 0
        ],
        'chat' => [
            'name' => 'チャット',
            'alias' => 'chat',
            'id' => 10,
            'parrent_id' => 0
        ],
        // 'advertising-management' => [
        //     'name' => '広告管理',
        //     'alias' => 'advertising-management',
        //     'id' => 11,
        //     'parrent_id' => 0
        // ],  // task no547

        'attendance-for-general-affairs' => [
            'name' => '勤怠DL',
            'alias' => 'attendance-for-general-affairs',
            'id' => 12,
            'parrent_id' => 0
        ],

        'edit-admin' => [
            'name' => '従業員リスト',
            'alias' => 'edit-admin',
            'id' => 13,
            'parrent_id' => 0
        ],
        'year-end-management-list' => [
            'name' => '年末調整',
            'alias' => 'year-end-management-list',
            'id' => 14,
            'parrent_id' => 0
        ],
        'stress-check' => [
            'name' => 'ストレスチェック',
            'alias' => 'stress-check',
            'id' => 15,
            'parrent_id' => 0
        ],
        'staff-paylist' => [
            'name' => '給与明細',
            'alias' => 'staff-paylist',
            'id' => 16,
            'parrent_id' => 0
        ],
        'my-staff-paylist' => [
            'name' => '自分の給与明細を見る',
            'alias' => 'my-staff-paylist',
            'id' => 116,
            'parrent_id' => 16
        ],
        'whiteboard' => [
            'name' => 'スプレッドシート',
            'alias' => 'whiteboard',
            'id' => 17,
            'parrent_id' => 0
        ],
        'shift-management' => [
            'name' => 'シフト管理',
            'alias' => 'shift-management',
            'id' => 20,
            'parrent_id' => 0
        ],
        'group-management' => [
            'name' => '部門管理',
            'alias' => 'group-management',
            'id' => 21,
            'parrent_id' => 0
        ],

        'order-management' => [
            'name' => '請求書',
            'alias' => 'order-management',
            'id' => 22,
            'parrent_id' => 0
        ],

        'employee-attendance' => [
            'name' => '従業員勤怠',
            'alias' => 'employee-attendance',
            'id' => 23,
            'parrent_id' => 0
        ],

        // 'admin-management' => [
        //     'name' => '管理ページ',
        //     'alias' => 'admin-management',
        //     'id' => 26,
        //     'parrent_id' => 0
        // ],
        //child
        'admin-management' => [
            'name' => 'システム内掲示板管理が開ける',
            'alias' => 'admin-management',
            'id' => 24,
            'parrent_id' => 1
        ],
        'setting-calendar' => [
            'name' => 'カレンダー設定ができる',
            'alias' => 'setting-calendar',
            'id' => 25,
            'parrent_id' => 1
        ],
        'bulletin-action' => [
            'name' => '掲示板アクションを操作できる',
            'alias' => 'bulletin-action',
            'id' => 51,
            'parrent_id' => 1
        ],
        'view-staffsearch-btn' => [
            'name' => '初期設定ができる',
            'alias' => 'view-staffsearch-btn',
            'id' => 57,
            'parrent_id' => 2
        ],
        'view-profile-access' => [
            'name' => '個人情報が開ける',
            'alias' => 'view-profile-access',
            'id' => 26,
            'parrent_id' => 2
        ],
        'edit-profile' => [
            'name' => '個人情報変更許可をだせる',
            'alias' => 'edit-profile',
            'id' => 27,
            'parrent_id' => 2
        ],
        'staffsearch-download-csv' => [
            'name' => 'CSVダウンロード',
            'alias' => 'staffsearch-download-csv',
            'id' => 92,
            'parrent_id' => 2
        ],
        'rubber-stamp' => [
            'name' => '総務印が押せる',
            'alias' => 'rubber-stamp',
            'id' => 28,
            'parrent_id' => 3
        ],
        // 'export-insurance-form' => [
        //     'name' => '保険加入用紙を出力することがができる',
        //     'alias' => 'export-insurance-form',
        //     'id' => 29,
        //     'parrent_id' => 5
        // ],
        'stamped-insurance' => [
            'name' => '実行印が押せる',
            'alias' => 'stamped-insurance',
            'id' => 29,
            'parrent_id' => 5
        ],
        'tab-staff-insurance' => [
            'name' => 'スタッフタブが開ける',
            'alias' => 'tab-staff-insurance',
            'id' => 80,
            'parrent_id' => 5
        ],
        'tab-admin-insurance' => [
            'name' => '従業員タブが開ける',
            'alias' => 'tab-admin-insurance',
            'id' => 81,
            'parrent_id' => 5
        ],
        'view-admin-insurance' => [
            'name' => '「従業員タブ」個人情報を表示する',
            'alias' => 'view-admin-insurance',
            'id' => 82,
            'parrent_id' => 5
        ],
        'view-staff-insurance' => [
            'name' => '「スタッフタブ」個人情報を表示する',
            'alias' => 'view-staff-insurance',
            'id' => 83,
            'parrent_id' => 5
        ],
        'report-label-setting' => [
            'name' => '初期設定ができる',
            'alias' => 'report-label-setting',
            'id' => 66,
            'parrent_id' => 8
        ],
        'setup-plan' => [
            'name' => '目標設定ができる',
            'alias' => 'setup-plan',
            'id' => 30,
            'parrent_id' => 8
        ],
        'setup-apply-form' => [
            'name' => '申請フォーマットの設定ができる',
            'alias' => 'setup-apply-form',
            'id' => 31,
            'parrent_id' => 9
        ],
        'view-management' => [
            'name' => '管理画面が見れる',
            'alias' => 'view-management',
            'id' => 32,
            'parrent_id' => 9
        ],
        'add-new-account' => [
            'name' => '新規アカウントの申請ができる',
            'alias' => 'add-new-account',
            'id' => 33,
            'parrent_id' => 13
        ],
        'edit-my-profile' => [
            'name' => 'プロフィールの編集ができる',
            'alias' => 'edit-my-profile',
            'id' => 34,
            'parrent_id' => 13
        ],
        'change-role-edit-profile' => [
            'name' => '個人情報変更許可が出せる',
            'alias' => 'change-role-edit-profile',
            'id' => 35,
            'parrent_id' => 13
        ],
        'view-all-group' => [
            'name' => 'すべての情報を閲覧できる',
            'alias' => 'view-all-group',
            'id' => 99,
            'parrent_id' => 14
        ],
        'view-management-staff' => [
            'name' => '従業員管理画面が開ける',
            'alias' => 'view-management-staff',
            'id' => 36,
            'parrent_id' => 14
        ],
        'view-list-staff' => [
            'name' => 'スタッフ管理画面が開ける',
            'alias' => 'view-list-staff',
            'id' => 37,
            'parrent_id' => 14
        ],
        'action-private-or-public' => [
            'name' => '公開・非公開ボタンが押せる',
            'alias' => 'action-private-or-public',
            'id' => 38,
            'parrent_id' => 14
        ],

        'access-stress-check' => [
            'name' => '従業員管理画面が開ける',
            'alias' => 'access-stress-check',
            'id' => 39,
            'parrent_id' => 15
        ],
        'access-stress-check-management' => [
            'name' => 'スタッフ管理画面が開ける',
            'alias' => 'access-stress-check-management',
            'id' => 40,
            'parrent_id' => 15
        ],
        'access-stress-public-private' => [
            'name' => '公開・非公開設定ができる',
            'alias' => 'access-stress-public-private',
            'id' => 41,
            'parrent_id' => 15
        ],
        'access-stress-btn-tightening' => [
            'name' => '締めのボタンが押せる',
            'alias' => 'access-stress-btn-tightening',
            'id' => 42,
            'parrent_id' => 15
        ],

        'csv-import-edit' => [
            'name' => 'CSV取り込み・編集ができる',
            'alias' => 'csv-import-edit',
            'id' => 43,
            'parrent_id' => 16
        ],
        'view-employee-pay-slip' => [
            'name' => '従業員の給与明細が見れる',
            'alias' => 'view-employee-pay-slip',
            'id' => 44,
            'parrent_id' => 16
        ],

        'view-commision' => [
            'name' => '歩合表読み込みができる',
            'alias' => 'view-commision',
            'id' => 45,
            'parrent_id' => 17
        ],
        'edit-commission' => [
            'name' => '歩合を表記させる',
            'alias' => 'edit-commission',
            'id' => 46,
            'parrent_id' => 17
        ],

        'group-edit' => [
            'name' => '編集ができる',
            'alias' => 'group-edit',
            'id' => 47,
            'parrent_id' => 21
        ],

        'view-csv-invoice' => [
            'name' => 'CSVの読み込みができる',
            'alias' => 'view-csv-invoice',
            'id' => 48,
            'parrent_id' => 22
        ],

        'employee-view-timecard' => [
            'name' => '従業員のタイムカード閲覧ができる',
            'alias' => 'employee-view-timecard',
            'id' => 49,
            'parrent_id' => 23
        ],
        'employee-setting' => [
            'name' => '設定画面が開ける',
            'alias' => 'employee-setting',
            'id' => 50,
            'parrent_id' => 23
        ],
        'employee-setting-delete' => [
            'name' => '申請の削除ができる',
            'alias' => 'employee-setting-delete',
            'id' => 555,
            'parrent_id' => 23
        ],
        'employee-attendance-approve-all' => [
            'name' => 'まとめて許可ボタンを表示する',
            'alias' => 'employee-attendance-approve-all',
            'id' => 556,
            'parrent_id' => 23
        ],
        'employee-attendance-request-list' => [
            'name' => '勤怠申請一覧を表示する',
            'alias' => 'employee-attendance-request-list',
            'id' => 557,
            'parrent_id' => 23
        ],
        'food-management' => [
            'name' => '食数収支管理',
            'alias' => 'food-management',
            'id' => 19,
            'parrent_id' => 0
        ],
        'business-class' => [
            'name' => '業務課',
            'alias' => 'business-class',
            'id' => 52, // 1 AFS
            'parrent_id' => 19
        ],
        'business-promotion-section' => [
            'name' => '業務推進課',
            'alias' => 'business-promotion-section',
            'id' => 53, // 7 AFS
            'parrent_id' => 19
        ],
        'manager' => [
            'name' => 'マネージャー',
            'alias' => 'manager',
            'id' => 54, // 2 AFS
            'parrent_id' => 19
        ],
        // 'edit-customer' => [
        //     'name' => '顧客管理',
        //     'alias' => 'edit-customer',
        //     'id' => 18,
        //     'parrent_id' => 0
        // ], // task no574
        'basic-business-search' => [
            'name' => '営業情報管理',
            'alias' => 'basic-business-search',
            'id' => 55,
            'parrent_id' => 0
        ],
        'basic-business-search-init-setting' => [
            'name' => '初期設定ができる',
            'alias' => 'basic-business-search-init-setting',
            'id' => 65,
            'parrent_id' => 55
        ],
        //no509 start
        // 'basic-business-search-download-csv' => [
        //     'name' => 'CSVダウンロード',
        //     'alias' => 'basic-business-search-download-csv',
        //     'id' => 94,
        //     'parrent_id' => 55
        // ],
        //no509 end
        'workflow-business' => [
            'name' => 'クラウドサインを追加',
            'alias' => 'workflow-business',
            'id' => 67,
            'parrent_id' => 0
        ],
        'company-cloud-sign-send' => [
            'name' => '「受信」タブを表記させる',
            'alias' => 'company-cloud-sign-send',
            'id' => 68,
            'parrent_id' => 67
        ],
        'workflow-business-list' => [
            'name' => '管理画面が開ける',
            'alias' => 'workflow-business-list',
            'id' => 69,
            'parrent_id' => 67
        ],
        'workflow-business-create-format' => [
            'name' => '社判登録ができる管理画面が開ける​',
            'alias' => 'workflow-business-create-format',
            'id' => 70,
            'parrent_id' => 67
        ],
        'workflow-business-format' => [
            'name' => 'フォーマット管理が開ける',
            'alias' => 'workflow-business-format',
            'id' => 71,
            'parrent_id' => 67
        ],
        'new-staff-paylist' => [
            'name' => '給与明細',
            
            'alias' => 'new-staff-paylist',
            'id' => 72,
            'parrent_id' => 0
        ],
        'csv-format-import-edit' => [
            'name' => 'CSV取り込み・編集ができる',
            'alias' => 'csv-format-import-edit',
            'id' => 73,
            'parrent_id' => 72
        ],
        'view-format-employee-pay-slip' => [
            'name' => '従業員の給与明細が見れる',
            'alias' => 'view-format-employee-pay-slip',
            'id' => 74,
            'parrent_id' => 72
        ],
        'admin-create-new-contract' => [
            'name' => '契約書を作成できる',
            'alias' => 'admin-create-new-contract',
            'id' => 75,
            'parrent_id' => 13
        ],
        'staff-create-new-contract' => [
            'name' => '契約書の作成ができる',
            'alias' => 'staff-create-new-contract',
            'id' => 76,
            'parrent_id' => 60
        ],
        'fieldstaffsearch-download-csv' => [
            'name' => ' CSVダウンロード',
            'alias' => 'fieldstaffsearch-download-csv',
            'id' => 93,
            'parrent_id' => 60
        ],
        'asmo' => [
            'name' => '食数収支管理(AFS)',
            'alias' => 'asmo',
            'id' => 77,
            'parrent_id' => 0
        ],
        'business-class-asmo' => [
            'name' => '業務課',
            'alias' => 'business-class-asmo',
            'id' => 78, // 1 AFS
            'parrent_id' => 77
        ],
        'business-promotion-section-asmo' => [
            'name' => '業務推進課',
            'alias' => 'business-promotion-section-asmo',
            'id' => 79, // 7 AFS
            'parrent_id' => 77
        ],
        'manager-asmo' => [
            'name' => 'マネージャー',
            'alias' => 'manager-asmo',
            'id' => 84, // 2 AFS
            'parrent_id' => 77
        ],
        'setup-view-report' => [
            'name' => '日週月報を閲覧できる',
            'alias' => 'setup-view-report',
            'id' => 88,
            'parrent_id' => 8
        ],
        'new-setup-view-report' => [
            'name' => '日週月報を閲覧できる',
            'alias' => 'new-setup-view-report',
            'id' => 89,
            'parrent_id' => 85
        ],
        'admin-edit-personal-info' => [
            'name' => '退職手続きができる',
            'alias' => 'admin-edit-personal-info',
            'id' => 90,
            'parrent_id' => 13
        ],
        'company_my_page' => [
            'name' => '企業マイページ',
            'alias' => 'company_my_page',
            'id' => 91,
            'parrent_id' => 0
        ],
        'admin_role_company' => [
            'name' => '企業myページの追加ができる',
            'alias' => 'admin_role_company',
            'id' => 97,
            'parrent_id' => 91
        ],
        'view-all-shift-management' => [
            /* [583]
            'name' => 'すべてのシフトを観覧できる',
            */
            'name' => 'すべてのシフトを閲覧できる',
            'alias' => 'view-all-shift-management',
            'id' => 96,
            'parrent_id' => 20
        ],
        'electronic-book' => [ 
            'name' => '電子帳簿保存法',
            'alias' => 'electronic-book',
            'id' => 98,
            'parrent_id' => 0
        ],

        // Task 668 - VuNV
        'file_management' => [
            'name' => 'ファイルアップロード​',
            'alias' => 'file_management',
            'id' => 100,
            'parrent_id' => 0
        ],
        'file_management_upload' => [
            'name' => 'ファイルをアップロードできる',
            'alias' => 'file_management_upload',
            'id' => 101,
            'parrent_id' => 100
        ],
        'file_management_download' => [
            'name' => 'ファイルをダウンロードできる',
            'alias' => 'file_management_download',
            'id' => 102,
            'parrent_id' => 100
        ],
        'file_management_delete' => [
            'name' => 'ファイルを削除できる',
            'alias' => 'file_management_delete',
            'id' => 103,
            'parrent_id' => 100
        ],
        'file_management_restore' => [
            'name' => '削除ファイルの復活ができる',
            'alias' => 'file_management_restore',
            'id' => 104,
            'parrent_id' => 100
        ],
        'groups_login_credentials' => [
            'name' => '部門管理（資格情報）',
            'alias' => 'groups_login_credentials',
            'id' => 114,
            'parrent_id' => 0
        ],
        'allow_groups_login_credentials' => [
            'name' => '資格情報の編集ができる',
            'alias' => 'allow_groups_login_credentials',
            'id' => 115,
            'parrent_id' => 114
        ],
        // current last id 115
    ],
];

?>
