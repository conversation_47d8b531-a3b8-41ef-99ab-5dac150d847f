<ul class="dd-list">
    <?php $__currentLoopData = $groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php $__currentLoopData = $group; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li  class="news-li dd-item" id="<?php echo e($child->id); ?>">
                <div class = "dd-container">
                    <div class="dd-handle custom-control custom-checkbox dd-handle-custom">
                        <button data-action="collapse" type="button"><i class="fa fas fa-minus"></i>Collapse</button>
                        <input type="checkbox" class="mr-1 custom-control-input" name="checkbox" value="<?php echo e($child->id); ?>" data-position="<?php echo e($child->position ?? ''); ?>" data-name="<?php echo e($child->name); ?>" data-data_type="<?php echo e($child->data_type ?? 'group'); ?>">
                        <label class="item-tree custom-control-label text-normal"  data-group-id="<?php echo e($child->id); ?>" for=""><?php echo e($child->name); ?></label>
                    </div>
                </div>
                <?php if($child->children): ?>
                    <?php echo $__env->make('Includes.filter-group-node',['childs' => $child->children], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
<?php /**PATH E:\TOOLS\laragon\www\control_management\app/Modules/\GroupManagement\Views/Group/load_modal_groups.blade.php ENDPATH**/ ?>