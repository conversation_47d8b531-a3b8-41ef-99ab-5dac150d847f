<!doctype html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<style type="text/css">

	table {
		padding: 2px 5px;
	}

	.rowheadl1, .rowheadl3,  .rowheadr3 {
		background-color: #becdf2;
		color: #000;
		border-left: 1px solid #000;
		border-right: 1px solid #000;
		border-top: 1px solid #000;
		border-bottom: 1px solid #000;
		text-align: center;
	}

	.rowheadl1x {
		background-color: #becdf2;
		color: #000;
		border-left: 1px solid #000;
		border-right: 1px solid #000;
		border-top: 1px solid #000;
		border-bottom: 1px solid #000;
		text-align: center;
		line-height: normal;		
	}

	.rowheadl1x1{
		line-height: 20px;
		background-color: #becdf2;
		color: #000;
		border-left: 1px solid #000;
		border-right: 1px solid #000;
		border-top: 1px solid #000;
		border-bottom: 1px solid #000;
		text-align: center;		
	}

	.rowheadr1x1{
		line-height: 20px;
		background-color: #becdf2;
		color: #000;
		border-left: 1px solid #000;
		border-right: 1px solid #000;
		border-top: 1px solid #000;
		border-bottom: 1px solid #000;
		text-align: center;		
	}	

	.rowheadl1z {
		background-color: #becdf2;
		color: #000;
		border-left: 1px solid #000;
		border-right: 1px solid #000;
		border-bottom: 1px solid #000;
		border-top: 1px solid #000;
		text-align: center;
		line-height: normal		
	}	

	.rowheadr1{
		background-color: #becdf2;
		color: #000;
		border-left: 0px solid #0000;
		border-right: 1px solid #000;
		border-top: 1px solid #000;
		border-bottom: 1px solid #000;
		text-align: center;
	}	
	
	.rowheadl3{
		border-bottom: 1px solid #000;
	}
	.rowheadr3{
		border-bottom: 1px solid #000;
	}	
	.rowheadr3 {
		border-left: 0px solid #000;
		border-right: 1px solid #000;
	}

	.rowdatal1, .rowdatal3, .rowdatar1, .rowdatal2, .rowdatar2, .rowdatar2b, .rowdatar3, .rowdatal3a {
		border-left: 1px solid #000;
		border-right: 1px solid #000;
		border-bottom: 1px solid #000;
		border-top: 1px solid #000;
		text-align: right;
		padding-right: 5px;
		line-height: 20px;
	}

	.rowdatar1{
		border-top: 1px solid #000;
		border-left: 0px solid #000;	
	}

	.rowdatal2{
		border-top: 1px solid #000;
		border-bottom: 1px solid #000;
	}		

	.rowdatal3{
		border-top: 1px solid #000;
		border-left: 1px solid #000;
		border-bottom: 1px solid #000;
	}	

	.rowdata4a{
		line-height: normal;
		text-align: left;
		border-bottom: 1px solid #000; 
		border-left: 1px solid #000;
	}		

	.rowdata4b{
		line-height: normal;
		text-align: left;
		border-bottom: 1px solid #000; 
		border-left: 0px solid #000;
		border-right: 1px solid #000;
	}			

	.rowdatar2{
		border-top: 1px solid #000;
		border-bottom: 0px solid #000;
		border-left: 0px solid #000;
		line-height: normal;
	}

	.rowdatar2a	{
		border-top: 1px solid #000;
		border-bottom: 0px solid #000;
		border-left: 0px solid #000;
		border-right: 1px solid #000;
		line-height: normal;
		text-align: right		
	}

	.rowdatar2b{
		border-top: 1px solid #000;
		border-left: 0px solid #000;
		line-height: normal;
	}	

	.rowdatar2c{
		border-top: 1px solid #000;
		border-right: 1px solid #000;
		line-height: normal;
		text-align: right;
	}

	.rowdatar3{
		border-left: 0px solid #000;
		border-top: 1px solid #000;
		border-bottom: 1px solid #000;
		line-height: normal;
	}	

	.rowdatar3b{
		border-left: 0px solid #000;
		border-top: 1px solid #000;
		border-bottom: 1px solid #000;
		line-height: normal;
		border-right: 1px solid #000;
		text-align: right;
		line-height: 20px;
	}

	.rowdatar3x{
		border-left: 0px solid #000;
		border-right: 1px solid #000;
		border-top: 1px solid #000;
		border-bottom: 1px solid #000;
		line-height: normal;
		text-align: right
	}		

	.rowdatal3a {
		line-height: normal;
		border-bottom: 1px solid #000; 
	}

	.title1{
		background-color: #1a367d;
		color: #fff;
		font-size: 10.5pt;
	}

	.lastrowhl1{
		background-color: #becdf2;

	}

	.pcell1{
		border:1px solid #becdf2;
	}
	.top-body {
		line-height: 0px
	}
</style>
</head>
<body>

<?php if(!empty($p_data)): ?>
	<?php
		$year = $p_data->cd_salary_year ?? 0;
        $month = $p_data->cd_salary_month ?? 0;
        $s_user_type = session()->get('s_user_type'); 
	?>
	<div class="top-body">
		<table border="0" style="padding: 10px 5px 10px 10px;">
			<tr>
				<td class="title1" style="width: 100px;"><?php echo e($year); ?>年<?php echo e($month); ?>月分</td>
				<td class="title1"><?php echo e($t_data->work_title); ?>明細書</td>
			</tr>
		</table>
	</div>
	

	<!-- 1st table -->
	<table border=0 width="785px;">
		<tr>
			<td class="rowheadl1" style="width: 20%">部門 ‐ 所属</td>
			<td class="rowheadr1" style="width: 20%">社員コード</td>
			<td class="rowheadr1" style="width: 60%; text-align: left;padding-left: 7px;">氏名</td>
		</tr>
		<tr>
			<td class="rowdata4a">&nbsp;<?php echo e($p_data->cd_salary_e); ?></td>
			<td class="rowdata4b">&nbsp;<?php echo e($p_data->cd_salary_a); ?></td>
			<td class="rowdata4b">&nbsp;<?php echo e($user_info->user_name ?? ''); ?>　様</td>
		</tr>
	</table>
	<br>
	

	<!-- 2nd table -->
	<table class="paysliptable" width="102%">
			<tr>
				<td colspan="7" style="line-height: 20px;">支給</td>
			</tr>
			<tr>
				<td class="rowheadl1" style="width: 14%"><div style="font-size:6pt;">&nbsp;</div>基本給</td>
				<td class="rowheadr1" style="width: 14%">
					役員報酬<br>役職手当
				</td>
				<td class="rowheadr1" style="width: 14%"><div style="font-size:6pt;">&nbsp;</div>皆勤手当</td>
				<td class="rowheadr1" style="width: 14%"><div style="font-size:6pt;">&nbsp;</div>勤務手当</td>
				<td class="rowheadr1" style="width: 14%"><div style="font-size:6pt;">&nbsp;</div>歩合給</td>
				<td class="rowheadr1" style="width: 14%"><div style="font-size:6pt;">&nbsp;</div>前月調整</td>
				<td class="rowheadr1" style="width: 14%"><div style="font-size:6pt;">&nbsp;</div> 技能手当</td>
			</tr>
			<tr>
				<td class="rowdatal1">
					&nbsp;<br>
					<?php echo e((!empty($p_data->cd_salary_ap) ? number_format($p_data->cd_salary_ap): '')); ?>

				</td>
				<td class="rowdatar1">
					<?php echo e((!empty($p_data->cd_salary_bp) ? number_format($p_data->cd_salary_bp): '')); ?>

                    <br>
                    <?php echo e((!empty($p_data->cd_salary_ar) ? number_format($p_data->cd_salary_ar): '')); ?>

				</td>
				<td class="rowdatar1">
					&nbsp;<br>
					<?php echo e((!empty($p_data->cd_salary_at) ? number_format($p_data->cd_salary_at): '')); ?>

				</td>
				<td class="rowdatar1">
					&nbsp;<br>
					<?php echo e((!empty($p_data->cd_salary_av) ? number_format($p_data->cd_salary_av): '')); ?>

				</td>
				<td class="rowdatar1">
					&nbsp;<br>
					<?php echo e((!empty($p_data->cd_salary_ax) ? number_format($p_data->cd_salary_ax): '')); ?>

				</td>
				<td class="rowdatar1">
					&nbsp;<br>
					<?php echo e(!empty($p_data->cd_salary_bv) ? number_format($p_data->cd_salary_bv): ''); ?>

				</td>
				<td class="rowdatar1">
					&nbsp;<br>
					<?php echo e(!empty($p_data->cd_salary_bb) ? number_format($p_data->cd_salary_bb): ''); ?>

				</td>
			</tr>
			<tr>
				<td class="rowheadl1">
					休業手当<br>休業補償
				</td>
				<td class="rowheadr1">
					補助手当<br>その他手当
				</td>
                <td class="rowheadr1"><div style="font-size:6pt;">&nbsp;</div>営業手当</td>
				<td class="rowheadr1">
					業務手当<br>時間外手当
				</td>
				<td class="rowheadr1"><div style="font-size:6pt;">&nbsp;</div>通勤手当</td>
				<td class="rowheadr1"><div style="font-size:6pt;">&nbsp;</div>不就労控除</td>
				<td class="rowheadr1"><div style="font-size:6pt;">&nbsp;</div>総支給額</td>
			</tr>
			<tr>
				<td class="rowdatal1" style="border-top-color: #000;">
					<?php echo e(!empty($p_data->cd_salary_cg) ? number_format($p_data->cd_salary_cg): ''); ?>

					<br>
					<?php echo e(!empty($p_data->cd_salary_ch) ? number_format($p_data->cd_salary_ch): ''); ?>

				</td>
				<td class="rowdatar3b">
					<?php echo e(!empty($p_data->cd_salary_ay) ? number_format($p_data->cd_salary_ay): ''); ?>

					<br>
					<?php echo e(!empty($p_data->cd_salary_bf) ? number_format($p_data->cd_salary_bf): ''); ?>

				</td>
				<td class="rowdatar3b">
					<br>
                    <?php echo e(!empty($p_data->cd_salary_bh) ? number_format($p_data->cd_salary_bh): ''); ?>

				</td>
				<td class="rowdatar3b">
					<?php echo e(!empty($p_data->cd_salary_bj) ? number_format($p_data->cd_salary_bj): ''); ?>

					<br>
					<?php echo e(!empty($p_data->cd_salary_ck) ? number_format($p_data->cd_salary_ck): ''); ?>

				</td>
				<td class="rowdatar3b">
					<br>
					<?php echo e(!empty($p_data->cd_salary_cl) ? number_format($p_data->cd_salary_cl): ''); ?>

                </td>
				<td class="rowdatar3b">
					<br>
					<?php echo e(!empty($p_data->cd_salary_cm) ? number_format($p_data->cd_salary_cm): ''); ?>

				</td>
				<td class="rowdatar3b">
					<br>
					<?php echo e(!empty($p_data->cd_salary_cp) ? number_format($p_data->cd_salary_cp): ''); ?>

				</td>
			</tr>
	</table>
	<br>


	<!-- 3rd table -->
	<table class="" width="102%">
			<tr>
				<td colspan="7" style="line-height: 20px;">控除</td>
			</tr>	
			<tr>
				<td class="rowheadl1x1" style="width: 14%;">健康保険</td>
				<td class="rowheadr1x1" style="width: 14%">厚生年金</td>
				<td class="rowheadr1x1" style="width: 14%">介護保険</td>
				<td class="rowheadr1x1" style="width: 14%">雇用保険</td>
				<td class="rowheadr1x1" style="width: 14%">保険料合計</td>
				<td class="rowheadr1x1" style="width: 14%">所得税</td>
				<td class="rowheadr1x1" style="width: 14%">住民税</td>
			</tr>
			<tr>
				<td class="rowdatal1"><?php echo e(!empty($p_data->cd_salary_cq) ? number_format($p_data->cd_salary_cq): ''); ?></td>
				<td class="rowdatar1"><?php echo e(!empty($p_data->cd_salary_cu) ? number_format($p_data->cd_salary_cu): ''); ?></td>
				<td class="rowdatar1"><?php echo e(!empty($p_data->cd_salary_cs) ? number_format($p_data->cd_salary_cs): ''); ?></td>
				<td class="rowdatar1"><?php echo e(!empty($p_data->cd_salary_cy) ? number_format($p_data->cd_salary_cy): ''); ?></td>
				<td class="rowdatar1"><?php echo e(!empty($p_data->cd_salary_da) ? number_format($p_data->cd_salary_da): ''); ?></td>
				<td class="rowdatar1"><?php echo e((!empty($p_data->cd_salary_db) ? number_format($p_data->cd_salary_db): '')); ?></td>
				<td class="rowdatar1"><?php echo e((!empty($p_data->cd_salary_dc) ? number_format($p_data->cd_salary_dc): '')); ?></td>
			</tr>
			<tr>
				<td class="rowheadl1">

					社保調整額<br>雇用調整額
				</td>
				<td class="rowheadr1">

				</td>
				<td class="rowheadr1"><div style="font-size:6pt;">&nbsp;</div> 社宅代</td>
				<td class="rowheadr1"><div style="font-size:6pt;">&nbsp;</div> </td>
				<td class="rowheadr1"><div style="font-size:6pt;">&nbsp;</div> </td>
				<td class="rowheadr1"><div style="font-size:6pt;">&nbsp;</div> </td>
				<td class="rowheadr1"><div style="font-size:6pt;">&nbsp;</div>控除計 </td>
			</tr>
			<tr>
				<td class="rowdatal3" style="border-top-color: #000;">
					<?php echo e(!empty($p_data->cd_salary_cr) ? number_format($p_data->cd_salary_cr): ''); ?><br>
                    <?php echo e(!empty($p_data->cd_salary_cz) ? number_format($p_data->cd_salary_cz): ''); ?>

				</td>
				<td class="rowdatar3b">
					
				</td>
				<td class="rowdatar3b">
					<br>
					<?php echo e(!empty($p_data->cd_salary_df) ? number_format($p_data->cd_salary_df): ''); ?>

				</td>
				<td class="rowdatar3b">
					
				</td>
				<td class="rowdatar3b">
					
				</td>
				<td class="rowdatar3b">
					
				</td>
				<td class="rowdatar3b" style="line-height: 20px;">
					<br>
					<?php echo e(!empty($p_data->cd_salary_eo) ? number_format($p_data->cd_salary_eo): ''); ?>

				</td>
			</tr>
	</table>
	<br>
	<br>

	<!-- 4th table -->
	<table class="paysliptable" width="102%">
			<tr>
				<td class="rowheadl1x" style="width: 14%">その他調整</td>
				<td class="rowheadr1" style="width: 14%"></td>
				<td class="rowheadr1" style="width: 14%">年末調整</td>
				<td class="rowheadr1" style="width: 14%">差引支給額</td>
				<td class="rowheadr1" style="width: 14%"></td>
				<td class="rowheadr1" style="width: 14%"></td>
				<td class="rowheadr1" style="width: 14%">定額減税額</td>
			</tr>
			<tr>
				<td class="rowdatal3a"><?php echo e((!empty($p_data->cd_salary_eq) ? number_format($p_data->cd_salary_eq): '')); ?></td>
				<td class="rowdatar3b">&nbsp;</td>
				<td class="rowdatar3b"><?php echo e((!empty($p_data->cd_salary_ep) ? number_format($p_data->cd_salary_ep): '')); ?></td>
				<td class="rowdatar3b"><?php echo e((!empty($p_data->cd_salary_eu) ? number_format($p_data->cd_salary_eu): '')); ?></td>
				<td class="rowdatar3b">&nbsp;</td>
				<td class="rowdatar3b">&nbsp;</td>
				<td class="rowdatar3b"><?php echo e((!empty($p_data->cd_salary_ev) ? number_format($p_data->cd_salary_ev): '')); ?></td>
			</tr>

	</table>
	


	<!-- 5th table -->
	<table border="0" width="102%">
		<tr>
			<td style="width: 95px;">有給使用日数 </td>
			<td style="width: 40px;"><?php echo e(sprintf("%.2f", $p_data->cd_salary_ak)); ?></td>
			<td style="width: 95px">有休残日数 </td>
			<td><?php echo e(sprintf("%.2f", $p_data->cd_salary_al)); ?></td>
		</tr>
		<!-- <tr>
			<td colspan="4">&nbsp;</td>
		</tr> -->

	</table>
	<br />

	<!-- 6th table -->
	<table border="0" width="777px">
		<tr>
			<td class="rowheadl1x" style="width: 9%;">労働日数</td>
			<td class="rowdatar2a" style="width: 9%"><?php echo e((!empty($p_data->cd_salary_m) ? sprintf("%.2f", $p_data->cd_salary_m) : '')); ?></td>
			<td style="width: 2%"></td>
			<td class="rowheadl1x" style="width: 9%">&nbsp;</td>
			<td class="rowheadr1" style="width: 9%">欠勤</td>
			<td class="rowheadr1" style="width: 9%">不就労</td>
			<td class="rowheadr1" style="width: 9%">平日普通</td>
			<td class="rowheadr1" style="width: 9%">平日深夜</td>
			<td class="rowheadr1" style="width: 9%">休日普通</td>
			<td class="rowheadr1" style="width: 9%">休日深夜</td>
			<td class="rowheadr1" style="width: 9%">法休普通</td>
			<td class="rowheadr1" style="width: 9%">法休深夜</td>
		</tr>
		<tr>
			<td class="rowheadl1x">出勤日数</td>
			<td class="rowdatar2a"><?php echo e((!empty($p_data->cd_salary_n) ? sprintf("%.2f", $p_data->cd_salary_n) : '')); ?></td>
			<td></td>
			<td class="rowheadl1x">時間</td>
			<td class="rowdatar2c">
				<?php echo e((!empty($p_data->cd_salary_p) && sprintf("%.2f", $p_data->cd_salary_p)!='0.00' ? sprintf("%.2f", $p_data->cd_salary_p) : '')); ?>

			</td>
			<td class="rowdatar2c">
				<?php echo e((!empty($p_data->cd_salary_r) && sprintf("%.2f", $p_data->cd_salary_r)!='0.00' ? sprintf("%.2f", $p_data->cd_salary_r) : '')); ?>

			</td>
			<td class="rowdatar2c">
				<?php echo e((!empty($p_data->cd_salary_t) && sprintf("%.2f", $p_data->cd_salary_t)!='0.00' ? sprintf("%.2f", $p_data->cd_salary_t) : '')); ?>

			</td>
			<td class="rowdatar2c">
				<?php echo e((!empty($p_data->cd_salary_v) && sprintf("%.2f", $p_data->cd_salary_v)!='0.00' ? sprintf("%.2f", $p_data->cd_salary_v) : '')); ?>

			</td>
			<td class="rowdatar2c">
				<?php echo e((!empty($p_data->cd_salary_x) && sprintf("%.2f", $p_data->cd_salary_x)!='0.00' ? sprintf("%.2f", $p_data->cd_salary_x) : '')); ?>

			</td>
			<td class="rowdatar2c">
				<?php echo e((!empty($p_data->cd_salary_z) && sprintf("%.2f", $p_data->cd_salary_z)!='0.00' ? sprintf("%.2f", $p_data->cd_salary_z) : '')); ?>

			</td>
			<td class="rowdatar2c">
				<?php echo e((!empty($p_data->cd_salary_ab) && sprintf("%.2f", $p_data->cd_salary_ab)!='0.00' ? sprintf("%.2f", $p_data->cd_salary_ab) : '')); ?>

			</td>
			<td class="rowdatar2c">
				<?php echo e((!empty($p_data->cd_salary_ad) && sprintf("%.2f", $p_data->cd_salary_ad)!='0.00' ? sprintf("%.2f", $p_data->cd_salary_ad) : '')); ?>

			</td>
		</tr>	
		<tr>
			<td class="rowheadl1z">出勤時数</td>
			<td class="rowdatar3x"><?php echo e((!empty($p_data->cd_salary_o) ? sprintf("%.2f", $p_data->cd_salary_o) : '')); ?></td>
			<td></td>
			<td class="rowheadl1z">金額</td>
			<td class="rowdatar3x">
				<?php echo e(!empty((int)$p_data->cd_salary_p*(int)$p_data->cd_salary_q) ? number_format(floor((float)$p_data->cd_salary_p*(float)$p_data->cd_salary_q)) : ''); ?>

			</td>
			<td class="rowdatar3x">
				<?php echo e(!empty((float)$p_data->cd_salary_r*(float)$p_data->cd_salary_s) ? number_format(floor((float)$p_data->cd_salary_r*(float)$p_data->cd_salary_s)) : ''); ?>

			</td>
			<td class="rowdatar3x">
				<?php echo e(!empty((float)$p_data->cd_salary_t*(float)$p_data->cd_salary_u) ? number_format(floor((float)$p_data->cd_salary_t*(float)$p_data->cd_salary_u)) : ''); ?>

			</td>
			<td class="rowdatar3x">
				<?php echo e(!empty((float)$p_data->cd_salary_v*(float)$p_data->cd_salary_w) ? number_format(floor((float)$p_data->cd_salary_v*(float)$p_data->cd_salary_w)) : ''); ?>

			</td>
			<td class="rowdatar3x">
				<?php echo e(!empty((float)$p_data->cd_salary_x*(float)$p_data->cd_salary_y) ? number_format(floor((float)$p_data->cd_salary_x*(float)$p_data->cd_salary_y)) : ''); ?>

			</td>
			<td class="rowdatar3x">
				<?php echo e(!empty((float)$p_data->cd_salary_z*(float)$p_data->cd_salary_aa) ? number_format(floor((float)$p_data->cd_salary_z*(float)$p_data->cd_salary_aa)) : ''); ?>

			</td>
			<td class="rowdatar3x">
				<?php echo e(!empty((float)$p_data->cd_salary_ab*(float)$p_data->cd_salary_ac) ? number_format(floor((float)$p_data->cd_salary_ab*(float)$p_data->cd_salary_ac)) : ''); ?>

			</td>
			<td class="rowdatar3x">
				<?php echo e(!empty((float)$p_data->cd_salary_ad*(float)$p_data->cd_salary_ae) ? number_format(floor((float)$p_data->cd_salary_ad*(float)$p_data->cd_salary_ae)) : ''); ?>

			</td>
		</tr>	
	</table>
	<?php $route_name = request()->route()->getname(); ?>
    <?php if(strpos($route_name, 'portal_site.') === false): ?>
	<table border="0" width="101%">
		<tr >
			<td style="padding-right:0px" align="right"><?php echo e($listCompany[$p_data->cd_salary_e]??""); ?></td>
		</tr>
	</table>
	<?php endif; ?>
    <br>
<?php endif; ?>
</body>
</html><?php /**PATH E:\TOOLS\laragon\www\control_management\resources\views/Staff/Paylist/Includes/payslip-data-tcpdf-admin.blade.php ENDPATH**/ ?>