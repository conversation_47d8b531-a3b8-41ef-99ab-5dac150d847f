[2025-07-28 16:10:19] local.ERROR: The file "E:\TOOLS\laragon\www\control_management\storage/近藤 雅俊.pdf" does not exist {"userId":1216,"exception":"[object] (Symfony\\Component\\HttpFoundation\\File\\Exception\\FileNotFoundException(code: 0): The file \"E:\\TOOLS\\laragon\\www\\control_management\\storage/近藤 雅俊.pdf\" does not exist at E:\\TOOLS\\laragon\\www\\control_management\\vendor\\symfony\\http-foundation\\File\\File.php:36)
[stacktrace]
#0 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\symfony\\http-foundation\\BinaryFileResponse.php(93): Symfony\\Component\\HttpFoundation\\File\\File->__construct('E:\\\\TOOLS\\\\larago...')
#1 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\symfony\\http-foundation\\BinaryFileResponse.php(52): Symfony\\Component\\HttpFoundation\\BinaryFileResponse->setFile('E:\\\\TOOLS\\\\larago...', 'attachment', false, true)
#2 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(165): Symfony\\Component\\HttpFoundation\\BinaryFileResponse->__construct('E:\\\\TOOLS\\\\larago...', 200, Array, true, 'attachment')
#3 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Controllers\\Staff\\Paylist\\PaylistController.php(1240): Illuminate\\Routing\\ResponseFactory->download('E:\\\\TOOLS\\\\larago...')
#4 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Staff\\Paylist\\PaylistController->downloadPayslipPDF(Object(Illuminate\\Http\\Request))
#5 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('downloadPayslip...', Array)
#6 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Staff\\Paylist\\PaylistController), 'downloadPayslip...')
#7 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#8 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#9 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckRolePortal.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckRolePortal->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'staff-paylist', 'new-staff-payli...')
#12 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\LoginMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\LoginMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckRoleAdmin.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckRoleAdmin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckOriginSite.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckOriginSite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 500, '1')
#22 E:\\TOOLS\\laragon\\www\\control_management\\app\\Http\\Middleware\\CheckPortal.php(107): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPortal->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 E:\\TOOLS\\laragon\\www\\control_management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 E:\\TOOLS\\laragon\\www\\control_management\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 E:\\TOOLS\\laragon\\www\\control_management\\server.php(21): require_once('E:\\\\TOOLS\\\\larago...')
#58 {main}
"} 
