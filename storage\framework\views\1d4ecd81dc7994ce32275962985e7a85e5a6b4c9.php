<?php
$i_userid = Session::get('i_userid');
$admin_id = $update_data[0]->admin_id;
$mail = Session::get('mail');
$admin_position_value = Session::get('admin_position_value');
$portal = session('portal');
$is_testctrl = strpos(request()->url(), "testctrl.") ? true : false;
$isAdminstrator = "";
if ($admin_id == $i_userid) {
    $isAdminstrator = Session::get('isAdminstrator') ? Session::get('isAdminstrator') : 2; //2 = selfprofile not admin

    $stt_tax_yearend = checkActiveTax(@$tax_belong->status_allow_yearend, @$tax_belong->status);
    $is_disabled = 'is_disabled';
    $link_adjusment = '#';
    if($stt_tax_yearend['code']){
        $is_disabled = '';
        $link_adjusment =  route('tax.adjust', $i_userid.'?is_admin=1');
    }
    //$link_adjusment = isset($tax_belong) ? ($tax_belong->status_belong ? route('tax.adjust', $i_userid.'?is_admin=1') : "#") : "#";
    //$link_adjusment = @$update_data[0]->is_allow_yearend ? route('tax.adjust', $i_userid) : "#";
}
$inputReadOnly = checkRoleUser(config("constants.ROLE.edit-my-profile.alias")) ? "" : "readonly";
$checkROLE = ($mail == "<EMAIL>" || $mail == "<EMAIL>") ? true : false;

$inputDisabled = checkRoleUser(config("constants.ROLE.edit-my-profile.alias")) ? "" : "disabled";
$is_edit_personal_info = checkRoleUser(config("constants.ROLE.admin-edit-personal-info.alias")) ? true : false;
$is_edit_profile = checkRoleUser(config("constants.ROLE.edit-my-profile.alias")) ? true : false;
$is_edit = ($admin_id == $i_userid) ? false : true;
$current_route = config('current_route');
?>

<style>
    .iziModal.hasScroll .iziModal-wrap
    {
        overflow-y: auto !important;
        overflow-x: hidden !important;
    }
    .iziModal.hasScroll .iziModal-wrap::-webkit-scrollbar {
        width: 10px;
        margin: 0 5px;
        background: #bcedf4;
    }
    .iziModal.hasScroll .iziModal-wrap::-webkit-scrollbar-thumb {
        border: 2px solid #bcedf4;
        background: #40bdd1;
        border-radius: 5px;
    }

    .is_disabled{
        background: #0d9eb9 !important;
    }

    .iziModal.hasScroll .iziModal-wrap::-webkit-scrollbar-thumb:hover {
        background: #40bdd1;
    }
    .tg-modal .custom-control-input{
        position: absolute;
    }
</style>

<div class='col-12 prof-supheader'>
    <h4 class='p-3 pr-5' style='color: #000'>
        <strong class='d-none d-sm-block float-left'>登録情報修正</strong>
        <strong class='float-right'>社員情報</strong>
    </h4>
</div>
<div class='col-12 prof-header'>
    <?php if(!empty($update_data[0]->admin_image)): ?>
    <?php
    $admin_image = $update_data[0]->admin_image;
    ?>
        <?php if(file_exists(public_path('images/adminProfile/' .$admin_image))): ?>
        <div class='prof-pic'>
            <img src="<?php echo e(asset('images/adminProfile/' . $admin_image . '')); ?>" id="profile_temp_image" class="" alt="User Image">
        </div>
        <?php else: ?>
        <div class='prof-pic'>
            <img src="<?php echo e(asset('images/AVATAR.png')); ?>" id="profile_temp_image" class="" alt="User Image">
        </div>
        <?php endif; ?>
    <?php else: ?>
    <div class='prof-pic'>
        <img src="<?php echo e(asset('images/AVATAR.png')); ?>" id="profile_temp_image" class="" alt="User Image">
    </div>
    <?php endif; ?>
    <div class='prof-details' style="padding-top:10px">
        <div class="row d-block d-sm-none" id="auprof-img-container3">
            <div class="profile__img__btn s1 col-md-12">
                <input type="hidden" name="au_image_file" value="<?php echo e(!empty($admin_image) ? $admin_image : ''); ?>">
                <p class="image-upload-profile-422">
                    <label for="admin-profile-picture2">
                        <img src="<?php echo e(asset('images/fa-edit-solid.PNG')); ?>"/>
                    </label>
                    <input type="file" class="form-control admin-profile-picture-xx" onclick="this.value=null;" value="<?php echo e(!empty($admin_image) ? $admin_image : ''); ?>" id="admin-profile-picture2" name="au_add_image">
                    <span>プロフィール写真の画像を選択してください</span>
                </p>
            </div>
        </div>
        <div class="row d-none d-sm-block" id="auprof-img-container4">
            <div style="height:100px;padding: top 10px;">
                <p class="NameText2">従業員プロフィール</p>
                <div class="name_overflow NameText">
                    <h2 style="color:white"><?php echo e($update_data[0]->name); ?></h2>
                </div>

            </div>
            <div class="editButtonProp" style="height:40px;margin-top:65px">
                <div class="profile__img__btn s1 col-md-12">
                    <input type="hidden" name="au_image_file" value="<?php echo e(!empty($admin_image) ? $admin_image : ''); ?>">
                    <p class="image-upload-profile-422">
                        <label for="admin-profile-picture1-edit">
                            <img src="<?php echo e(asset('images/fa-edit-solid.PNG')); ?>"/>
                        </label>
                        <input type="button" id="admin-profile-picture1-edit" onclick="EditProfileImg(this)" data-img="<?php echo e(!empty($admin_image) ? $admin_image : ''); ?>" data-admin_id="<?php echo e($admin_id); ?>">
                        <label for="admin-profile-picture1">
                            <img src="<?php echo e(asset('images/fa-search-solid.png')); ?>"/>
                        </label>
                        <input type="file" class="form-control admin-profile-picture-xx" onclick="this.value=null;" value="<?php echo e(!empty($admin_image) ? $admin_image : ''); ?>" id="admin-profile-picture1" name="au_add_image">
                        <span  class="NameText2">プロフィール写真の画像を選択してください</span>
                    </p>
                </div>
            </div>

        </div>
    </div>
    <div class='prof-stamp'>
        <?php $admin_stamp = $update_data[0]->admin_stamp; ?>
        <div class="stamp-wrapper mt-4 text-center">
            
            <div class="stamp-drop-area emp-stamp-container" onclick="editStampForm(this)" data-aid="<?php echo e($aid); ?>"  data-tcontainer="#empolyee-stamp-view" style="cursor: pointer;">
                <div id="empolyee-stamp-view" class="stampholder" data-seal="<?php echo e($adminseal); ?>">
                </div>
            </div>
            
            <p for="stamp">ハンコ</p>
            <div class="row chat-button-cont s2">
                <div class="col-12 my-3">
                    <?php if(!empty($room_id)): ?>
                    <a target="_blank" class="chat-button s2" href="<?php echo e('chat?room_id='.$room_id); ?>">チャット</a>
                    <?php else: ?>
                    <a target="_blank" class="chat-button s2" href="<?php echo e('chat?user_id='.$admin_id); ?>">チャット</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <div class='prof-extra s1 text-center'>
        <div class="row chat-button-cont s1">
            <div class="col-12 my-3">
                <?php if(!empty($room_id)): ?>
                <a target="_blank" class="chat-button s1" href="<?php echo e('chat?room_id='.$room_id); ?>">チャット</a>
                <?php else: ?>
                <a target="_blank" class="chat-button s1" href="<?php echo e('chat?user_id='.$admin_id); ?>">チャット</a>
                <?php endif; ?>
            </div>
        </div>

        <?php if( $is_edit_profile || $i_userid == $admin_id ): ?>
        <div div class="mt-2 w-100 btn-personal-info">
            <fieldset class="mb-2" style="border: 1px solid white;">
                <a class="form-control btn" style="background-color:#0d9eb9; color: #fff;">個人情報</a>
                <div class="form-group d-flex d-sm-block d-md-flex">
                    
                    <div class="col-6 col-sm-12 col-md-6 profile-links  pl-3 pr-0 px-sm-1 pl-md-3 pr-md-0">
                        
                        <?php if($i_userid == $admin_id): ?>
                            <?php if($get_is_admin_profile_update[0]->is_admin_profile_update == true || $is_edit_profile == true): ?>
                                <a href="<?php echo e(url('edit-admin-details')); ?>?admin_id=<?php echo e($admin_id); ?>" target="_blank" class="btn btn-outline btn-block" style="border:1px solid white;width:100%; background:white; color: #40bdd0" data-admin_id="<?php echo e($admin_id); ?>">入力</a>
                            <?php else: ?>
                                <a class="btn btn_info" style="border:1px solid white;background-color:#40bdd0;width:100%;color:white" data-admin_id="<?php echo e($admin_id); ?>" disabled>入力</a>
                            <?php endif; ?>
                        <?php else: ?>
                            
                            <?php if( $is_edit_profile == true): ?>
                                <a href="<?php echo e(url('edit-admin-details')); ?>?admin_id=<?php echo e($admin_id); ?>" target="_blank" class="btn btn-outline btn-block" style="border:1px solid white;width:100%; background:white;color: #40bdd0" data-admin_id="<?php echo e($admin_id); ?>">入力</a>
                            <?php else: ?>
                                <a class="btn btn_info" style="border:1px solid white;background-color:#40bdd0;width:100%;color:white" data-admin_id="<?php echo e($admin_id); ?>" disabled>入力</a>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-6 col-sm-12 col-md-6 profile-links pl-3 pr-3 px-sm-1 px-md-3">
                        
                        <?php if($i_userid == $admin_id): ?>
                            <a href="<?php echo e(url('view-admin-details')); ?>?admin_id=<?php echo e($admin_id); ?>" target="_blank" class="btn mr-2 btn-outline btn-block" style="border:1px solid white;width:100%;" data-admin_id="<?php echo e($admin_id); ?>" data-view_only="1">閲覧</a>
                        <?php else: ?>
                            
                            <?php if($is_edit_profile == true): ?>
                                <a href="<?php echo e(url('view-admin-details')); ?>?admin_id=<?php echo e($admin_id); ?>" target="_blank" class="btn mr-2 btn-outline btn-block" style="border:1px solid white;width:100%;" data-admin_id="<?php echo e($admin_id); ?>" data-view_only="1">閲覧</a>
                            <?php else: ?>
                                <a class="btn mr-2 btn_info" style="border:1px solid white;background-color:#40bdd0;width:100%" data-admin_id="<?php echo e($admin_id); ?>" data-view_only="1" disabled>閲覧</a>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </fieldset>
        </div>
        <?php endif; ?>
        
        
        
        
        
    </div>
</div>
<div class='col-12 prof-sub-header'>
    <div class='d-none d-sm-block'>
        <a href="password-reset?admin_id=<?php echo e($i_userid); ?>" class="sub-links"><i class="fas fa-external-link-alt"></i> パスワード変更 </a>
        <?php if(checkRolePortal('staff-paylist') || checkRolePortal('new-staff-paylist')): ?>
        <a href='#' data-izimodal-open="#modal-confirm-password" class="sub-links"><i class="fas fa-external-link-alt"></i> 給与明細 </a>
        <?php endif; ?>
        <?php if(checkRolePortal('year-end-management-list')): ?>
        <a href='<?php echo e(@$link_adjusment); ?>' class="sub-links <?php echo e(@$is_disabled); ?>"><i class="fas fa-external-link-alt"></i> 年末調整を行う </a>
        <?php endif; ?>
        <?php if(checkRolePortal('stress-check')): ?>
        <a href="<?php echo e($update_data[0]->is_allow_stress_check? url('stress-check-questionnaire') : 'javascript:void(0)'); ?>" class="sub-links <?php echo e($update_data[0]->is_allow_stress_check ? '': 'is_disabled'); ?>" <?php echo e($update_data[0]->is_allow_stress_check? '': 'disabled'); ?> ><i class="fas fa-external-link-alt"></i> ストレスチェックを行う </a>
        <?php endif; ?>
        <!--<a href="<?php echo e(url('stress-check-details')); ?>" class="sub-links"><i class="fas fa-external-link-alt"></i> ストレスチェックの内容確認する </a>-->
        <a href="<?php echo e(route('contracts-user', ['user_type'=> 'admin','user_id' => $i_userid ])); ?>"  class="sub-links"><i class="fas fa-external-link-alt"></i> 契約書を確認 </a>

    </div>
    <div class='d-block d-sm-none'>
        <div class='sub-select' onclick='displaylinks()'>
            <span>年末調整を行う</span>
            <ul id='this-sublinks' class='sub-select-opt'>
                <li><a href="password-reset?admin_id=<?php echo e($i_userid); ?>"><i class="fas fa-external-link-alt"></i> パスワード変更 </a></li>
                <?php if(checkRolePortal('staff-paylist')): ?>
                    <li><a href='#' data-izimodal-open="#modal-confirm-password"><i class="fas fa-external-link-alt"></i> 給与明細 </a></li>
                <?php endif; ?>
                <?php if(checkRolePortal('year-end-management-list')): ?>
                    <li  class="<?php echo e(@$is_disabled); ?>"><a href='<?php echo e(@$link_adjusment); ?>'><i class="fas fa-external-link-alt"></i> 年末調整を行う </a></li>
                <?php endif; ?>
                <?php if(checkRolePortal('stress-check')): ?>
                <li><a href="<?php echo e($update_data[0]->is_allow_stress_check? url('stress-check-questionnaire') : 'javascript:void(0)'); ?>" <?php echo e($update_data[0]->is_allow_stress_check? '': 'disabled'); ?> ><i class="fas fa-external-link-alt"></i> ストレスチェックを行う </a></li>
                <?php endif; ?>
            <!--<li><a href="<?php echo e(url('stress-check-details')); ?>"><i class="fas fa-external-link-alt"></i> ストレスチェックの内容確認する </a></li>-->
                <li><a href="<?php echo e(route('contracts-user', ['user_type'=> 'admin','user_id' => $i_userid ])); ?>"><i class="fas fa-external-link-alt"></i> 契約書を確認 </a></li>
            </ul>
        </div>
    </div>
</div>
<div class='col-12 prof-form'>
    <div class="profile__info__container">
        <!-- 登録情報の編集 08042021 ここから Thet Thiri San(<EMAIL>)-->
        <div class="row mb-3">
            <input type="hidden" id='s_admin_id' name='s_admin_id' value='<?php echo e($i_userid); ?>'>
            <div class="form-group col-12 col-md-6">
                <label for="">氏名 : </label>
                <span class="required">必須</span>
                <input type="text" class="form-control" id="" placeholder="" name="s_name_like" value="<?php echo e($update_data[0]->name); ?>" required <?php echo e($inputReadOnly); ?>>
            </div>
            <div class="form-group col-12 col-md-6">
                <label for="">ユーザー名 : </label>
                <span class="required">必須</span>
                <input type="text" class="form-control" id="s_username" placeholder="" name="s_username" value="<?php echo e($update_data[0]->username); ?>" required <?php echo e($inputReadOnly); ?>>
            </div>
        </div>
        <?php echo $__env->make('GroupManagement::Group.form_group_select',['prefix_select_id' => 'select_group_admin_', 'wrap_id'=>'form_group_admin'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


        <div class="row mb-4">
            <div class="form-group col-12 col-md-12">
                <label for="">ステータス : </label>
                <span class="required">必須</span>
                <select class="form-control sel sel_pos" name="s_position_id" id="" required <?php echo e($inputDisabled); ?>>
                        <option value=""></option>
                    <?php $__empty_1 = true; $__currentLoopData = $admin_position; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ap): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <option value="<?php echo e($ap->position_id); ?>"
                            <?php if($update_data[0]->position_id == $ap->position_id): ?> selected="selected" <?php endif; ?>
                            ><?php echo e($ap->label); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <option value="NULL">error</option>
                    <?php endif; ?>
                </select>
            </div>
        </div>


        <div class="row align-items-center mb-2">
            <div class="col-12 col-md-auto">
                <label for="">入社日 : </label>
                <span class="required">必須</span>
            </div>

            <div class="form-group col-md-auto flex-grow-1">
                <?php
                $date = explode('-', $update_data[0]->enter_date);
                $y = $date[0];
                $m = $date[1];
                $d = $date[2];
                ?>

                <div class="d-flex align-items-center">
                    <select id="year" name="s_year" class="form-control sel sel_date" required <?php echo e($inputDisabled); ?>>
                            <option value="">年</option>
                        <?php echo e($last= date('Y')-120); ?>

                        <?php echo e($now = date('Y')); ?>

                        <?php for($i = $now; $i >= $last; $i--): ?>
                        <option value="<?php echo e($i); ?>" <?php if($y == $i): ?> selected="selected" <?php endif; ?>>
                                <?php echo e($i); ?>年</option>
                        <?php endfor; ?>
                    </select>

                    <select id="month" name="s_month" class="form-control sel sel_date mx-4" required <?php echo e($inputDisabled); ?>>
                            <option value="">月</option>
                        <?php for($i = 1; $i <= 12; $i++): ?>
                        <option value="<?php echo e($i); ?>" <?php if($m == $i): ?> selected="selected" <?php endif; ?>><?php echo e($i); ?>月</option>
                        <?php endfor; ?>
                    </select>

                    <select id="day" name="s_day" class="form-control sel sel_date" required <?php echo e($inputDisabled); ?>>
                            <option value="">日</option>
                        <?php for($i = 1; $i <= 31; $i++): ?>
                        <option value="<?php echo e($i); ?>" <?php if($d == $i): ?> selected="selected" <?php endif; ?>><?php echo e($i); ?>日</option>
                        <?php endfor; ?>
                    </select>
                </div>
            </div>
        </div>

        <div class="row mb-3">
            <div class="form-group col-12 col-md-6">
                <label for="" class="chk_flag_label">Tel① :</label>
                <!-- <span class="required">必須</span>    -->
                <input type="hidden" name="" value="">
                <input type="tel" class="form-control " id="" placeholder="" name="s_tel1_like" value="<?php echo e($update_data[0]->tel1); ?>" onkeypress="return onlyNumberKey(event)">
            </div>

            <div class="form-group col-12 col-md-6">
                <label for="" class="chk_flag_label" >Tel② :</label>
                <input type="tel" class="form-control " id="" placeholder="" name="s_tel2_like" value="<?php echo e($update_data[0]->tel2); ?>" onkeypress="return onlyNumberKey(event)">
            </div>
        </div>

        <div class="row mb-3">
            <div class="form-group col-12 col-md-6">
                <label for="">Mailアドレス①[ID] :</label>
                <span class="required">必須</span>
                <input type="hidden" name="" value="">
                <input type="text" class="form-control " id="" placeholder="" name="s_mail1_like" value="<?php echo e($update_data[0]->mail); ?>" required <?php echo e($inputReadOnly); ?>>

            </div>

            <div class="form-group col-12 col-md-6">
                <label for="">Mailアドレス②[ID] :</label>
                <input type="hidden" name="" value="">
                <input type="text" class="form-control " id="" placeholder="" name="s_mail2_like" value="<?php echo e($update_data[0]->mail2); ?>">
            </div>
        </div>

        <?php
        $list = !empty($update_data) ? explode(',', $update_data[0]->role) : [];
        ?>
        <?php echo $__env->make('Includes.roles', ["edit"=> false,"role" => $role, "dataPicked" => $list , "class" => "authority-checkbox list-roles", "name" => "s_role[]", "prefix_id" => "edit-", "page_action" => "profile-detail", 'readonly' => $inputReadOnly ,'checkROLE' => $checkROLE], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="row mb-3"> </div>

        <div class="row mb-3">
            <div class="form-group col-12 col-md-6">
                <label for="" class="chk_flag_label">趣味 : </label>
                <textarea class="form-control " id="s_hobby" name="s_hobby" rows="4" cols="50"><?php echo e($update_data[0]->hobby); ?></textarea>
            </div>
            <div class="form-group col-12 col-md-6">
                <label for="" class="chk_flag_label">目標 : </label>
                <textarea class="form-control " id="s_target" name="s_target" rows="4" cols="50"><?php echo e($update_data[0]->target); ?></textarea>
            </div>
        </div>

        <div class="row mb-3 mb_profile_afs">
            <div class="form-group col">
                <label for="" class="chk_flag_label">自己PR : </label>
                <textarea class="form-control " id="s_remark" name="s_remark" rows="4" cols="50"><?php echo e($update_data[0]->remark); ?></textarea>
            </div>
        </div>

        <div class="profile-btn-group">
            <button class="btn btn-sm-block btn-light mr-2 mb-lg-2" data-iziModal-close>キャンセル</button>
            <button class="btn btn-sm-block btn-primary mr-2 mb-lg-2" type="button" id="change-profile-details">更新</button>
        </div>
    </div>
</div>


<script src="<?php echo e(asset('js/Admin/roles_user.js')); ?>"></script>
<script>
    function onlyNumberKey(evt) {

        // Only ASCII character in that range allowed
        var ASCIICode = (evt.which) ? evt.which : evt.keyCode
        if (ASCIICode > 31 && (ASCIICode < 48 || ASCIICode > 57))
            return false;
        return true;
    }
    function displaylinks()
    {
        $('#this-sublinks').css('display', 'block');
    }

    $(document).mouseup(function (e)
    {
        var container = $("#this-sublinks");

        // if the target of the click isn't the container nor a descendant of the container
        if (!container.is(e.target) && container.has(e.target).length === 0)
        {
            container.css('display', 'none');
        }
    });

    $(document).on('click','.is_disabled',function(){
        swal("","現在、このボタンはロックされています。","success");
    });
</script>
<?php /**PATH E:\TOOLS\laragon\www\control_management\resources\views/Profile/new-profile/view-admin-data-ar.blade.php ENDPATH**/ ?>