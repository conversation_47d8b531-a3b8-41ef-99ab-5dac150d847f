<?php

namespace App\Http\Controllers\Staff\Paylist;

use App\Http\Controllers\Controller;
use App\Imports\PaylistImport;
use App\Models\Staff\Paylist\PaylistModel;
use Illuminate\Http\Request;
use Elibyy\TCPDF\Facades\TCPDF as TCPDF;
use Mail, DB;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv;
use Illuminate\Support\LazyCollection;
use App\Models\Admin\Admin;
use stdClass;
use TCPDF_FONTS;
use Response;
use Carbon\Carbon;
use App\Http\Requests\VALIDATOR_FILE;
use App\Models\Paylist\ImportEmployerCodeModel;
use Illuminate\Support\Facades\Auth;
use App\Helpers\Helper;

class PaylistController extends Controller
{

    function getRole(){
        $arr_role = explode(',',session()->get('role'));
        return array(
            'arr_role'      => $arr_role
        );
    }

    function belongPayslip(){
        $belongs =  array(
            "001101000020"      =>"第一営業部営業一課部長",
            "010000000011"      =>"従業員常務取締役",
            "001301000020"      =>"第三営業部第一課部長",
            "10003"             =>"本社3課",
            "22002"             =>"名古屋営業所名古屋営業所２課",
            "001302000070"      =>"第三営業部営業二課社員",
            "001301000070"      =>"第三営業部営業一課社員",
            "40004000"          =>"仙台営業所",
            "141020"            =>"総合人材事業部部長",
            "004001000030"      =>"仙台営業所第一課所長",
            "001102000040"      =>"第一営業部営業二課課長",
            "003100000030"      =>"関西支社営業一課支社長",
            "300130"            =>"大阪営業所第一課所長",
            "33004"             =>"大阪営業所大(営)人材サービス課",
            "010000000009"      =>"従業員役員",
            "140070"            =>"ITｿﾘｭｰｼｮﾝ事業部社員",
            "006100000030"      =>"北海道支社営業一課支社長",
            "002005000070"      =>"名古屋営業所人材社員",
            "004100000040"      =>"東北支社営業一課課長",
            "190070"            =>"不動産事業部社員",
            "110240"            =>"第一営業部第二課課長",
            "002500000080"      =>"中部支社人材開発部パート",
            "16"                =>"本社ｷｯｽﾞﾌﾟﾚｲｽ経堂",
            "160040"            =>"総務経理課課長",
            "10008"             =>"本社ITｴﾝｼﾞﾆｱ",
            "003005000070"      =>"大阪営業所人材ｻｰﾋﾞｽ社員",
            "141070"            =>"総合人材事業部社員",
            "110140"            =>"第一営業部営業一課課長",
            "001500000070"      =>"人材ｻｰﾋﾞｽ課社員",
            "510030"            =>"九州支社営業一課所長",
            "610070"            =>"北海道支社営業一課社員",
            "44001"             =>"仙台営業所仙台営業所１課",
            "400170"            =>"仙台営業所第一課社員",
            "141060"            =>"総合人材事業部主任",
            "21000"             =>"中部支社営業一課",
            "130240"            =>"第三営業部営業二課課長",
            "001900000070"      =>"不動産事業部社員",
            "130120"            =>"第三営業部営業一課部長",
            "160080"            =>"総務経理課パート",
            "002200000040"      =>"中部支社営業二課課長",
            "000015000100"      =>"総合人材事業部スタッフ",
            "1000010"           =>"従業員代表取締役",
            "002500000040"      =>"中部支社人材開発部課長",
            "30003000"          =>"大阪営業所",
            "22008"             =>"名古屋営業所ITｿﾘｭｰｼｮﾝ事業部１課",
            "11100"             =>"S-本社 第一部スタッフ",
            "005500000070"      =>"九州支社人材開発部社員",
            "120270"            =>"第二営業部第二課社員",
            "002100000070"      =>"営業一課社員",
            "002100000070"      =>"中部支社営業一課社員",
            "130170"            =>"第三営業部営業一課社員",
            "20100"             =>"S-名古屋スタッフ",
            "150070"            =>"人材ｻｰﾋﾞｽ課社員",
            "000030000100"      =>"S-大阪スタッフ",
            "001500000080"      =>"人材開発部パート",
            "300570"            =>"大阪営業所人材ｻｰﾋﾞｽ社員",
            "120"               =>"本社第２営業部",
            "004500000070"      =>"東北支社人材開発部社員",
            "140041"            =>"ITｿﾘｭｰｼｮﾝ事業部課長代理",
            "007100000070"      =>"中国四国支社営業一課社員",
            "310030"            =>"関西支社営業一課所長",
            "610030"            =>"北海道支社営業一課支社長",
            "001201000070"      =>"第二営業部営業一課社員",
            "002100000050"      =>"営業一課副所長",
            "001410000080"      =>"総合人材事業部パート",
            "004001000070"      =>"仙台営業所第一課社員",
            "001102000040"      =>"第一営業部第二課課長",
            "002300000070"      =>"営業三課社員",
            "410070"            =>"東北支社営業一課社員",
            "132"               =>"本社第３営業部２課",
            "550070"            =>"九州支社人材開発部社員",
            "30100"             =>"S-大阪スタッフ",
            "200170"            =>"名古屋営業所第一課社員",
            "001101000070"      =>"第一営業部営業一課社員",
            "001301000070"      =>"第三営業部第一課社員",
            "001400000070"      =>"ITｿﾘｭｰｼｮﾝ事業部社員",
            "110170"            =>"第一営業部第一課社員",
            "10002"             =>"本社第2営業部",
            "001600000080"      =>"業務管理本部パート",
            "310040"            =>"関西支社営業一課課長",
            "001301000020"      =>"第三営業部営業一課部長",
            "005100000030"      =>"九州支社営業一課支社長",
            "001201000040"      =>"第二営業部第一課課長",
            "001202000070"      =>"第二営業部第二課社員",
            "10007"             =>"本社医師紹介室",
            "100009"            =>"従業員役員",
            "200240"            =>"名古屋営業所第二課課長",
            "001302000070"      =>"第三営業部第二課社員",
            "110120"            =>"第一営業部第一課部長",
            "000070000100"      =>"S-中国四国支社スタッフ",
            "001400000041"      =>"ITｿﾘｭｰｼｮﾝ事業部課長代理",
            "110240"            =>"第一営業部営業二課課長",
            "210030"            =>"中部支社営業一課支社長",
            "22001"             =>"名古屋営業所名古屋営業所１課",
            "40100"             =>"S-仙台スタッフ",
            "122"               =>"本社第２営業部２課",
            "003500000070"      =>"関西支社人材開発部社員",
            "410030"            =>"東北支社営業一課支社長",
            "250060"            =>"中部支社人材開発部主任",
            "250070"            =>"中部支社人材開発部社員",
            "003100000040"      =>"関西支社営業一課課長",
            "120170"            =>"第二営業部営業一課社員",
            "150080"            =>"人材開発部パート",
            "001600000071"      =>"業務管理本部契約社員",
            "002004000070"      =>"名-ITｿﾘｭｰｼｮﾝ事業部社員",
            "240070"            =>"中部-ITｿﾘｭｰｼｮﾝ事業部社員",
            "130"               =>"本社第３営業部",
            "001600000070"      =>"総務経理課社員",
            "350070"            =>"関西支社人材開発部社員",
            "130120"            =>"第三営業部第一課部長",
            "001101000040"      =>"第一営業部第一課課長",
            "000040000100"      =>"S-東北支社スタッフ",
            "001202000040"      =>"第二営業部営業二課課長",
            "002001000040"      =>"名古屋営業所第一課課長",
            "22000"             =>"名古屋営業所",
            "002400000070"      =>"中部-ITｿﾘｭｰｼｮﾝ事業部社員",
            "002002000070"      =>"名古屋営業所第二課社員",
            "12"                =>"本社営業２課",
            "001600000040"      =>"業務管理本部課長",
            "120120"            =>"第二営業部営業一課部長",
            "10001"             =>"本社1課",
            "001201000020"      =>"第二営業部営業一課部長",
            "002100000030"      =>"中部支社営業一課所長",
            "000015000100"      =>"S-総合人材事業部スタッフ",
            "120140"            =>"第二営業部第一課課長",
            "210070"            =>"中部支社営業一課社員",
            "22004"             =>"名古屋営業所名(営)人材サービス課",
            "002100000030"      =>"営業一課所長",
            "002200000040"      =>"営業二課課長",
            "220040"            =>"中部支社営業二課課長",
            "000020000100"      =>"S-中部支社スタッフ",
            "130170"            =>"第三営業部第一課社員",
            "160070"            =>"業務管理本部社員",
            "120240"            =>"第二営業部営業二課課長",
            "130240"            =>"第三営業部第二課課長",
            "004100000030"      =>"東北支社営業一課支社長",
            "000011000100"      =>"S-本社 第一部スタッフ",
            "13"                =>"本社営業３課",
            "200150"            =>"名古屋営業所第一課副所長",
            "210030"            =>"中部支社営業一課所長",
            "001410000070"      =>"総合人材派遣事業部社員",
            "750070"            =>"中国四国人材開発部社員",
            "006100000070"      =>"北海道支社営業一課社員",
            "110120"            =>"第一営業部営業一課部長",
            "11"                =>"本社営業１課",
            "001410000070"      =>"総合人材事業部社員",
            "160071"            =>"業務管理本部契約社員",
            "10002"             =>"本社2課",
            "002200000070"      =>"営業二課社員",
            "140040"            =>"ITｿﾘｭｰｼｮﾝ事業部課長",
            "001600000070"      =>"業務管理本部社員",
            "005100000030"      =>"九州支社営業一課所長",
            "22003"             =>"名古屋営業所名古屋営業所３課",
            "001410000060"      =>"総合人材事業部主任",
            "710030"            =>"中国四国支社営業一課支社長",
            "002001000070"      =>"名古屋営業所第一課社員",
            "001202000070"      =>"第二営業部営業二課社員",
            "14100"             =>"S-本社 ITスタッフ",
            "002002000040"      =>"名古屋営業所第二課課長",
            "000014000100"      =>"S-本社 ITスタッフ",
            "12"                =>"本社第２営業部",
            "150"               =>"本社総務経理課",
            "110170"            =>"第一営業部営業一課社員",
            "001101000070"      =>"第一営業部第一課社員",
            "001600000040"      =>"総務経理課課長",
            "000020000100"      =>"S-名古屋スタッフ",
            "110270"            =>"第一営業部第二課社員",
            "003100000070"      =>"関西支社営業一課社員",
            "004005000070"      =>"仙台営業所人材ｻｰﾋﾞｽ社員",
            "13"                =>"本社第３営業部",
            "160070"            =>"総務経理課社員",
            "000050000100"      =>"S-九州スタッフ",
            "000040000100"      =>"S-仙台スタッフ",
            "002100000030"      =>"中部支社営業一課支社長",
            "004100000070"      =>"営業一課社員",
            "120240"            =>"第二営業部第二課課長",
            "001302000040"      =>"第三営業部第二課課長",
            "000030000100"      =>"S-関西支社スタッフ",
            "650070"            =>"北海道人材開発部社員",
            "003500000080"      =>"関西支社人材開発部パート",
            "004100000030"      =>"東北支社営業一課所長",
            "1000011"           =>"従業員常務取締役",
            "400570"            =>"仙台営業所人材ｻｰﾋﾞｽ社員",
            "001201000070"      =>"第二営業部第一課社員",
            "130270"            =>"第三営業部営業二課社員",
            "003100000030"      =>"営業一課所長",
            "710070"            =>"中国四国支社営業一課社員",
            "001202000040"      =>"第二営業部第二課課長",
            "007100000030"      =>"中国四国支社営業一課支社長",
            "007500000070"      =>"中国四国人材開発部社員",
            "13100"             =>"S-本社 第三部スタッフ",
            "001600000080"      =>"総務経理課パート",
            "181"               =>"本社ITｿﾘｭｰｼｮﾝ事業部１課",
            "200570"            =>"名古屋営業所人材社員",
            "001101000040"      =>"第一営業部営業一課課長",
            "001301000040"      =>"第三営業部第一課課長",
            "001400000040"      =>"ITｿﾘｭｰｼｮﾝ事業部課長",
            "15"                =>"本社総務課",
            "112"               =>"本社第１営業部２課",
            "000012000100"      =>"S-本社 第二部スタッフ",
            "10003"             =>"本社第3営業部",
            "410030"            =>"東北支社営業一課所長",
            "10001"             =>"本社第1営業部",
            "003001000070"      =>"大阪営業所第一課社員",
            "001102000070"      =>"第一営業部第二課社員",
            "130140"            =>"第三営業部営業一課課長",
            "200130"            =>"名古屋営業所第一課所長",
            "010000000070"      =>"従業員社員",
            "005100000070"      =>"九州支社営業一課社員",
            "20002000"          =>"名古屋営業所",
            "110140"            =>"第一営業部第一課課長",
            "001201000040"      =>"第二営業部営業一課課長",
            "160080"            =>"業務管理本部パート",
            "310070"            =>"関西支社営業一課社員",
            "002001000030"      =>"名古屋営業所第一課所長",
            "1000070"           =>"従業員社員",
            "510030"            =>"九州支社営業一課支社長",
            "410040"            =>"東北支社営業一課課長",
            "18"                =>"本社ITｿﾘｭｰｼｮﾝ事業部",
            "006100000030"      =>"北海道支社営業一課所長",
            "002100000040"      =>"営業一課課長",
            "000040000100"      =>"S-東北スタッフ",
            "002100000040"      =>"中部支社営業一課課長",
            "001410000020"      =>"総合人材事業部部長",
            "200140"            =>"名古屋営業所第一課課長",
            "14"                =>"本社人材サービス課",
            "550040"            =>"九州支社人材開発部課長",
            "000060000100"      =>"Ｓ－北海道支社スタッフ",
            "003100000030"      =>"関西支社営業一課所長",
            "002500000070"      =>"中部支社人材開発部社員",
            "000013000100"      =>"S-本社 第三部スタッフ",
            "110270"            =>"第一営業部営業二課社員",
            "450070"            =>"東北支社人材開発部社員",
            "350080"            =>"関西支社人材開発部パート",
            "005500000040"      =>"九州支社人材開発部課長",
            "200270"            =>"名古屋営業所第二課社員",
            "110"               =>"本社第１営業部",
            "020000000070"      =>"新卒内定者社員",
            "002200000070"      =>"中部支社営業二課社員",
            "001101000020"      =>"第一営業部第一課部長",
            "250080"            =>"中部支社人材開発部パート",
            "44000"             =>"仙台営業所",
            "150070"            =>"人材開発部社員",
            "44004"             =>"仙台営業所仙(営)人材サービス課",
            "002500000060"      =>"中部支社人材開発部主任",
            "001500000080"      =>"人材ｻｰﾋﾞｽ課パート",
            "12100"             =>"S-本社 第二部スタッフ",
            "006500000070"      =>"北海道人材開発部社員",
            "001201000020"      =>"第二営業部第一課部長",
            "003100000070"      =>"営業一課社員",
            "11000"             =>"本社",
            "120120"            =>"第二営業部第一課部長",
            "510070"            =>"九州支社営業一課社員",
            "400130"            =>"仙台営業所第一課所長",
            "310030"            =>"関西支社営業一課支社長",
            "33001"             =>"大阪営業所大阪営業所１課",
            "33000"             =>"大阪営業所",
            "004100000030"      =>"営業一課所長",
            "120140"            =>"第二営業部営業一課課長",
            "004100000070"      =>"東北支社営業一課社員",
            "002100000000"      =>"中部支社営業一課",
            "002100000000"      =>"営業一課",
            "130140"            =>"第三営業部第一課課長",
            "300170"            =>"大阪営業所第一課社員",
            "220070"            =>"中部支社営業二課社員",
            "001500000070"      =>"人材開発部社員",
            "121"               =>"本社第２営業部１課",
            "210040"            =>"中部支社営業一課課長",
            "120170"            =>"第二営業部第一課社員",
            "111"               =>"本社第１営業部１課",
            "001102000070"      =>"第一営業部営業二課社員",
            "130270"            =>"第三営業部第二課社員",
            "200470"            =>"名-ITｿﾘｭｰｼｮﾝ事業部社員",
            "140"               =>"本社人材サービス課",
            "003001000030"      =>"大阪営業所第一課所長",
            "120270"            =>"第二営業部営業二課社員",
            "001302000040"      =>"第三営業部営業二課課長",
            "001301000040"      =>"第三営業部営業一課課長",
            "004500000080"      =>"東北支社人材開発部パート",
            "131"               =>"本社第３営業部１課",
            "002001000050"      =>"名古屋営業所第一課副所長",
            "17"                =>"本社医師紹介室",
            "11"                =>"本社第１営業部"
        );
        return $belongs;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    
    public function show(Request $request)
    {   
        if(!isset($request->new_staff_paylist)){
            $request->new_staff_paylist = 0;
        }
        $getRole = $this->getRole();
        $arr_role = $getRole['arr_role'];

        // if((!in_array('accountant', $arr_role) && !in_array('dispatch_business', $arr_role)) || !checkRoleUser(config("constants.ROLE.access-stress-check.alias")) ){
        // if(!checkRoleUser(config("constants.ROLE.staff-paylist.alias")) ){
        //     return redirect('/');
        // }
        $format_list_for_modal          = PaylistModel::get_document_approval_format();
        return view($request->new_staff_paylist == 0 ? 'Staff.Paylist.paylist' : 'Staff.Paylist.new-paylist', [
            'list_role' => $arr_role,
            'modal_format'  => !empty($format_list_for_modal) ? $format_list_for_modal : [],
        ]);
    }

    public function openPaySlipTab(Request $request)
    {

        $belongs        = $this->belongPayslip();
        $tab_opened     = $request->tab_opened;
        if($tab_opened=="payslip-tab")
        {
            return view('Staff.Paylist.Includes.tab-payslip',['belongs' => $belongs]);
        }
        elseif($tab_opened=="employee-master-tab")
        {
            $section_belong_list = PaylistModel::getEmployeeSectionBelongList();
            return view('Staff.Paylist.Includes.tab-employee-master',[
                'section_belong_list' => $section_belong_list
            ]);
        }
    }

    //CSV UPLOAD FUNCTIONS
    public function uploadPaylistCSV(Request $req)
    {
        $pay_type = $req->pay_type;
        if(!empty($pay_type)){
            if(!checkRoleUser(config("constants.ROLE.csv-format-import-edit.alias")) ){
                return back()->with('fail','あなたはすることを許可されていません');
            }
        }else{
            if(!checkRoleUser(config("constants.ROLE.csv-import-edit.alias")) ){
                return back()->with('fail','あなたはすることを許可されていません');
            }
        }
      
        $salary_type    = $req->salary_type;
        $pay_salary     = $req->file('pay_salary');
        $year           = (int)$req->year;
        $month          = (int)$req->month;
       
        session(['is_enter_password' => $year]);
       
        if(empty($pay_salary)){
            return back()->with('fail','ファイルを選択して下さい');
        }
        $ext            = $pay_salary->getClientOriginalExtension();
        if($ext !='csv'){
            return back()->with('fail','アップロード可能なファイルはcsvのみです');
        }
        $path = $fileName = null;
        // start import
        DB::beginTransaction();
        try {
            $arr_staff_not_exist =  [];
            $content_csv = file_get_contents($pay_salary);

            /* Lưu file import  - start */
            $pay_type_val       = $pay_type == null ? 0 : 1 ;
            $fileFormat         = 'pay_type_'.$pay_type_val."_".$year.$month.'_'.randomFileName();
            $path               = makeDirPortal('data_salary');
            $fileName           = $fileFormat.'_'.$pay_salary->getClientOriginalName();
            $pay_salary->move($path,$fileName);
            /* Lưu file import  - end */

            $content_csv_converted = iconv('SJIS','UTF-8//IGNORE',$content_csv);
            //var_dump(mb_detect_encoding($content_csv, ['SJIS'], true));die; return false nếu ko phải SJIS
            $data_rows = array_map('str_getcsv', explode("\n", $content_csv_converted));
            unset($data_rows[0]); // remove the csv headings

            //for new payslip module
            if(!empty($pay_type)){

                if(empty($data_rows[1][0]) || empty($data_rows[1][3])  || empty($data_rows[1][6]) || strlen($data_rows[1][6]) < 5  ){
                    return back()->with('fail','無効なファイル');
                }
    
                $year_month_import  = $data_rows[1][6] ?? null;
                $year_csv           = (int)substr($year_month_import,0,4);
                $month_csv          = (int)substr($year_month_import,4);
    
                if($salary_type==0){
                    if($year_csv != $year || $month_csv != $month){
                        return back()->with('fail','対象支給年月で選択している年月と給与csvファイルの年月が違います');
                    }
                }else{
                    if($year_csv != $year){
                        return back()->with('fail','対象支給年月で選択している年月と給与csvファイルの年月が違います');
                    }
                }
    
                // Delete old data
                $_where = array(
                    'cd_salary_type'    => $salary_type,
                    'cd_salary_year'    => (string)$year_csv,
                    'cd_salary_month'   => (string)$month_csv,
                    'pay_type'          => 1
                ); 
               
                DB::table('pay_salary_data')->where($_where)->delete();
                $all_format = DB::table('payslip_format_header')->whereNull('delete_date')->get()->toArray();
                $format_err = 0;
                if(!empty($data_rows)){
                    LazyCollection::make(function () use(&$data_rows, $pay_salary, $salary_type, $year, $month, $all_format) {
                        foreach($data_rows as $index => $data_row){
                            yield $data_row;
                        }
                    })->chunk(100)->each(function ($data_rows) use(&$arr_staff_not_exist, $salary_type, $all_format, $year, $month) {
                        
                        $data_salary_admin = $data_salary_staff = [];
                        $data_admin_id = $data_staff_id = [];
                        
                        foreach ($data_rows as $index => $row) {
                            if(isset($row[0]) && !empty($row[0])){
    
                                $year_month_csv = $year_csv = $month_csv = 0;
                                if(isset($row[6])){
                                    $year_month_csv = $row[6];
                                    $year_csv = (int)substr($year_month_csv,0,4);
                                    $month_csv = (int)substr($year_month_csv,4);
                                }
                                $b = isset($row[1]) ? (int)$row[1] : 0;

                                $format_id = $row[3] ?? 0;
                                $found = array_filter($all_format,function($v,$k) use ($format_id){
                                    return $v->format_id== $format_id;
                                  },ARRAY_FILTER_USE_BOTH);
                                $data_value = [];
                                if(!empty($found)){
                                    $data_salary =array_values($found);
                                    $data_salary = json_decode($data_salary[0]->value);
                                    for($i= 0; $i< (count($data_salary) + 8); $i++){
                                        $row_cnt = 9 + $i;
                                        array_push($data_value,['index' => $i, 'value' => array_key_exists($row_cnt,$row) ? $row[$row_cnt] : '']);
                                    }
                                }else{
                                    return back()->with('fail','フォーマットIDが存在していません !'); 
                                    $format_err = 1;
                                }
                                $cd_salary_a = $row[0] ?? null;
                                if($cd_salary_a != null && !empty($cd_salary_a) && $year_csv == $year && $month_csv == $month){
                                    $data_item = array(
                                        'cd_salary_type'    => $salary_type,
                                        'pay_type'          => 1,
    
                                        "cd_salary_a"       => $row[0] ?? null,
                                        "cd_salary_b"       => $row[1] ?? null,
                                        "cd_salary_c"       => $row[2] ?? null,
                                        "cd_salary_d"       => $row[3] ?? null,
                                        "cd_salary_e"       => $row[4] ?? null,
                                        "cd_salary_f"       => $row[5] ?? null,
                                        "cd_salary_g"       => $row[6] ?? null,
                                        "cd_salary_h"       => $row[7] ?? null,
                                        "cd_salary_i"       => $row[8] ?? null,
    
                                        "cd_salary_year"    => $year_csv,
                                        "cd_salary_month"   => $month_csv,
                                        "value"             => json_encode($data_value),
                                        "create_date"       => date('Y-m-d h:i:s'),
                                        'created_by'        => auth()->user()->admin_id
                                    );
        
                                    if($b==100){
                                        $data_salary_staff[$cd_salary_a] =  $data_item;
                                        $data_staff_id[] = $cd_salary_a;
                                    }
        
                                    if($b==1){
                                        $data_salary_admin[$cd_salary_a] =  $data_item;
                                        $data_admin_id[] = $cd_salary_a;
                                    }
                                }
                            }
                        }

                        // Điều kiện check tồn tại dữ liệu lương của 1 nhân viên
                        $_condition = array(
                            'cd_salary_type'    => $salary_type,
                            'cd_salary_year'    => $year,
                            'cd_salary_month'   => $month,
                            'pay_type'          => 1
                        );
                        
                        // Check staff not exist
                        if(!empty($data_staff_id)){
                            $staff_exist = PaylistModel::getStaffs($data_staff_id);
                            $staff_not_exist_id = array_diff($data_staff_id,$staff_exist);
                            if(!empty($staff_not_exist_id)){
                                foreach($staff_not_exist_id as $sv){
                                    $arr_staff_not_exist[$sv] =  $data_salary_staff[$sv]['cd_salary_b']??"";
                                    // Remove staff not exist
                                    unset($data_salary_staff[$sv]);
                                }
                            }
                            // Thêm dữ liệu lương
                            $this->insertNewSalaryData($data_salary_staff, $_condition);
                        }   
    
                        // Check admin not exist
                        if(!empty($data_admin_id)){
                            $admin_exist = Admin::getAdmins($data_admin_id);
                            $admin_not_exist_id = array_diff($data_admin_id,$admin_exist);
                            if(!empty($admin_not_exist_id)){
                                foreach($admin_not_exist_id as $sv){
                                    $arr_staff_not_exist[$sv] =  $data_salary_admin[$sv]['cd_salary_b']??"";
                                    // Remove staff not exist
                                    unset($data_salary_admin[$sv]);
                                }
                            }
                            // Thêm dữ liệu lương
                            $this->insertNewSalaryData($data_salary_admin, $_condition);
                        }   
                    });
                }
               
                DB::commit();
                $staff_not_exist_msg = '';
                if(!empty($arr_staff_not_exist)){
                    foreach($arr_staff_not_exist as $index => $v){
                        if($index==0){
                            $staff_not_exist_msg = $index."  ".$v."<br>";
                        }else{
                            $staff_not_exist_msg .= $index."  ".$v."<br>";
                        }
                    }
                }
                if(!empty($format_err)){
                    $staff_not_exist_msg .= "フォーマットIDが存在していません!";
                }
            }else{
                if(empty($data_rows[1][0]) || empty($data_rows[1][7]) || strlen($data_rows[1][7]) < 5  ){
                    return back()->with('fail','無効なファイル');
                }
    
                $year_month_import  = $data_rows[1][7] ?? "";
                $year_csv           = (int)substr($year_month_import,0,4);
                $month_csv          = (int)substr($year_month_import,4);

                if($salary_type==0){
                    if($year_csv != $year || $month_csv != $month){
                        return back()->with('fail','対象支給年月で選択している年月と給与csvファイルの年月が違います');
                    }
                }else{
                    if($year_csv != $year){
                        return back()->with('fail','対象支給年月で選択している年月と給与csvファイルの年月が違います');
                    }
                }
    
                // Điều kiện xóa dữ liệu cũ trước khi upload mới
                $_where = array(
                    'cd_salary_type'    => $salary_type,
                    'cd_salary_year'    => (string)$year_csv,
                    'cd_salary_month'   => (string)$month_csv,
                    'pay_type'          => 0
                ); 

                // Kiểm tra xem trong file import cột BZ có mấy loại giá trị trong 2 giá trị 1 và 100
                $count_number_type_data_bz = [];
                if(!empty($data_rows)){
                    foreach ($data_rows as $data) {
                        $data_bz = isset($data[77]) ? (int)$data[77] : 0;
                        if($data_bz > 0 && !in_array($data_bz, $count_number_type_data_bz)){
                            $count_number_type_data_bz[] = $data_bz;
                            if( count($count_number_type_data_bz) > 1 ){
                                break;
                            }
                        }
                    }
                }
                // TH file import chỉ có 1 loại giá trị cột BZ thì xóa dòng có giá trị = BZ
                if(count($count_number_type_data_bz)==1){
                    $_where['cd_salary_bz'] = $count_number_type_data_bz[0];
                }
                /* Xóa dữ liệu bảng lương cũ */
                DB::table('pay_salary_data')->where($_where)->delete();
                
                if(!empty($data_rows)){
                    LazyCollection::make(function () use(&$data_rows) {
                        foreach($data_rows as $data_row){
                            yield $data_row;
                        }
                    })->chunk(100)->each(function ($data_rows) use(&$arr_staff_not_exist, $salary_type, $year, $month) {
                        
                        $data_salary_admin = $data_salary_staff = [];
                        $data_admin_id = $data_staff_id = [];
                        
                        foreach ($data_rows as $row) {
                            if(isset($row[0]) && !empty($row[0])){

                                $year_month_csv = $year_csv = $month_csv = 0;
                                if(isset($row[7])){
                                    $year_month_csv     = $row[7];
                                    $year_csv           = (int)substr($year_month_csv,0,4);
                                    $month_csv          = (int)substr($year_month_csv,4);
                                }
         
                                $cd_salary_bz = isset($row[77]) ? (int)$row[77] : 0;
                                $cd_salary_a = $row[0] ?? null;

                                /* Có dữ liệu cd_salary_a, cd_salary_bz và có tháng năm trùng với tháng năm chọn import mới được import */
                                if(!empty($cd_salary_a) && $cd_salary_bz > 0 && $year_csv == $year && $month_csv == $month){
                                    
                                    $data_item = $this->getDataSalary($row);
                                    $data_item['cd_salary_type']    = $salary_type;
                                    $data_item['cd_salary_h']       = $year_month_csv;
                                    $data_item['cd_salary_bz']      = $cd_salary_bz ;
                                    $data_item['cd_salary_year']    = $year_csv;
                                    $data_item['cd_salary_month']   = $month_csv;

                                    if($cd_salary_bz==100){
                                        $data_salary_staff[$cd_salary_a]    = $data_item;
                                        $data_staff_id[]                    = $cd_salary_a;
                                    }
        
                                    if($cd_salary_bz==1){
                                        $data_salary_admin[$cd_salary_a]    = $data_item;
                                        $data_admin_id[]                    = $cd_salary_a;
                                    }
                                }
                            }
                        }

                        // Điều kiện check tồn tại dữ liệu lương của 1 nhân viên
                        $_condition = array(
                            'cd_salary_type'    => $salary_type,
                            'cd_salary_year'    => $year,
                            'cd_salary_month'   => $month,
                            'pay_type'          => 0
                        );
                        
                       // Loại bỏ những tài khoản staff không tồn tại trong DB
                        if(!empty($data_staff_id)){
                            $staff_exist = PaylistModel::getStaffs($data_staff_id);
                            $staff_not_exist_id = array_diff($data_staff_id,$staff_exist);
                            if(!empty($staff_not_exist_id)){
                                foreach($staff_not_exist_id as $sv){
                                    $arr_staff_not_exist[$sv] =  $data_salary_staff[$sv]['cd_salary_b']??"";
                                    unset($data_salary_staff[$sv]);
                                }
                            }
                            // Thêm dữ liệu lương
                            $this->insertSalaryData($data_salary_staff, $_condition);
                        }   
    
                        // Loại bỏ những tài khoản admin không tồn tại trong DB
                        if(!empty($data_admin_id)){
                            $admin_exist = Admin::getAdmins($data_admin_id);
                            $admin_not_exist_id = array_diff($data_admin_id,$admin_exist);
                            if(!empty($admin_not_exist_id)){
                                foreach($admin_not_exist_id as $sv){
                                    $arr_staff_not_exist[$sv] =  $data_salary_admin[$sv]['cd_salary_b']??"";
                                    unset($data_salary_admin[$sv]);
                                }
                            }
                            // Thêm dữ liệu lương
                            $this->insertSalaryData($data_salary_admin, $_condition);
                        }   
                    });
                }
               
                DB::commit();
                /* Hiển thị những tài khoản không tồn tại */
                $staff_not_exist_msg = '';
                if(!empty($arr_staff_not_exist)){
                    foreach($arr_staff_not_exist as $index => $v){
                        if($index==0){
                            $staff_not_exist_msg = $index."  ".$v."<br>";
                        }else{
                            $staff_not_exist_msg .= $index."  ".$v."<br>";
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            DB::rollback();
            /* Xóa file upload nếu trong quá trình xử lý xảy ra lỗi */ 
            if(!empty($path) && !empty($fileName)){
                deleteFile($path.'/'.$fileName);
            }
            return back()->with('fail','エラーインポートファイル');
        }

        $status = '【完了】'.$year.'年'.$month.'月分[社員/スタッフ] CSVファイルの取り込みが完了しました';
        if($salary_type==1){
            $txt_month = $month == 1 ? '①' : '➁';
            $status = '【完了】'.$year.'年'.$txt_month.'分[社員/スタッフ] CSVファイルの取り込みが完了しました';
        }
        // end import
        $data_with = [
            'status'            => $status,
            'year'              => $year,
            'month'             => $month
        ];
        if(!empty($staff_not_exist_msg)){
            $data_with['staff_not_exist'] = $staff_not_exist_msg;
        }
        return back()->with($data_with);
    }

    function getDataSalary($row){
        return array(
            "cd_salary_a"       => $row[0] ?? null,
            "cd_salary_b"       => $row[1] ?? null,
            "cd_salary_c"       => $row[2] ?? null,
            "cd_salary_d"       => $row[3] ?? null,
            "cd_salary_e"       => $row[4] ?? null,
            "cd_salary_f"       => $row[5] ?? null,
            "cd_salary_g"       => $row[6] ?? null,
    
            "cd_salary_i"       => $row[8] ?? null,
            "cd_salary_j"       => $row[9] ?? null,
            "cd_salary_k"       => $row[10] ?? null,
            "cd_salary_l"       => $row[11] ?? null,
            "cd_salary_m"       => $row[12] ?? null,
            "cd_salary_n"       => $row[13] ?? null,
            "cd_salary_o"       => $row[14] ?? null,
            "cd_salary_p"       => $row[15] ?? null,
            "cd_salary_q"       => $row[16] ?? null,
            "cd_salary_r"       => $row[17] ?? null,
            "cd_salary_s"       => $row[18] ?? null,
            "cd_salary_t"       => $row[19] ?? null,
            "cd_salary_u"       => $row[20] ?? null,
            "cd_salary_v"       => $row[21] ?? null,
            "cd_salary_w"       => $row[22] ?? null,
            "cd_salary_x"       => $row[23] ?? null,
            "cd_salary_y"       => $row[24] ?? null,
            "cd_salary_z"       => $row[25] ?? null,

            "cd_salary_aa"      => $row[26] ?? null,
            "cd_salary_ab"      => $row[27] ?? null,
            "cd_salary_ac"      => $row[28] ?? null,
            "cd_salary_ad"      => $row[29] ?? null,
            "cd_salary_ae"      => $row[30] ?? null,
            "cd_salary_af"      => $row[31] ?? null,
            "cd_salary_ag"      => $row[32] ?? null,
            "cd_salary_ah"      => $row[33] ?? null,
            "cd_salary_ai"      => $row[34] ?? null,
            "cd_salary_aj"      => $row[35] ?? null,
            "cd_salary_ak"      => $row[36] ?? null,
            "cd_salary_al"      => $row[37] ?? null,
            "cd_salary_am"      => $row[38] ?? null,
            "cd_salary_an"      => $row[39] ?? null,
            "cd_salary_ao"      => $row[40] ?? null,
            "cd_salary_ap"      => $row[41] ?? null,
            "cd_salary_aq"      => $row[42] ?? null,
            "cd_salary_ar"      => $row[43] ?? null,
            "cd_salary_as"      => $row[44] ?? null,
            "cd_salary_at"      => $row[45] ?? null,
            "cd_salary_au"      => $row[46] ?? null,
            "cd_salary_av"      => $row[47] ?? null,
            "cd_salary_aw"      => $row[48] ?? null,
            "cd_salary_ax"      => $row[49] ?? null,
            "cd_salary_ay"      => $row[50] ?? null,
            "cd_salary_az"      => $row[51] ?? null,

            "cd_salary_ba"      => $row[52] ?? null,
            "cd_salary_bb"      => $row[53] ?? null,
            "cd_salary_bc"      => $row[54] ?? null,
            "cd_salary_bd"      => $row[55] ?? null,
            "cd_salary_be"      => $row[56] ?? null,
            "cd_salary_bf"      => $row[57] ?? null,
            "cd_salary_bg"      => $row[58] ?? null,
            "cd_salary_bh"      => $row[59] ?? null,
            "cd_salary_bi"      => $row[60] ?? null,
            "cd_salary_bj"      => $row[61] ?? null,
            "cd_salary_bk"      => $row[62] ?? null,
            "cd_salary_bl"      => $row[63] ?? null,
            "cd_salary_bm"      => $row[64] ?? null,
            "cd_salary_bn"      => $row[65] ?? null,
            "cd_salary_bo"      => $row[66] ?? null,
            "cd_salary_bp"      => $row[67] ?? null,
            "cd_salary_bq"      => $row[68] ?? null,
            "cd_salary_br"      => $row[69] ?? null,
            "cd_salary_bs"      => $row[70] ?? null,
            "cd_salary_bt"      => $row[71] ?? null,
            "cd_salary_bu"      => $row[72] ?? null,
            "cd_salary_bv"      => $row[73] ?? null,
            "cd_salary_bw"      => $row[74] ?? null,
            "cd_salary_bx"      => $row[75] ?? null,
            "cd_salary_by"      => $row[76] ?? null,
           
            "cd_salary_ca"      => $row[78] ?? null,
            "cd_salary_cb"      => $row[79] ?? null,
            "cd_salary_cc"      => $row[80] ?? null,
            "cd_salary_cd"      => $row[81] ?? null,
            "cd_salary_ce"      => $row[82] ?? null,
            "cd_salary_cf"      => $row[83] ?? null,
            "cd_salary_cg"      => $row[84] ?? null,
            "cd_salary_ch"      => $row[85] ?? null,
            "cd_salary_ci"      => $row[86] ?? null,
            "cd_salary_cj"      => $row[87] ?? null,
            "cd_salary_ck"      => $row[88] ?? null,
            "cd_salary_cl"      => $row[89] ?? null,
            "cd_salary_cm"      => $row[90] ?? null,
            "cd_salary_cn"      => $row[91] ?? null,
            "cd_salary_co"      => $row[92] ?? null,
            "cd_salary_cp"      => $row[93] ?? null,
            "cd_salary_cq"      => $row[94] ?? null,
            "cd_salary_cr"      => $row[95] ?? null,
            "cd_salary_cs"      => $row[96] ?? null,
            "cd_salary_ct"      => $row[97] ?? null,
            "cd_salary_cu"      => $row[98] ?? null,
            "cd_salary_cv"      => $row[99] ?? null,
            "cd_salary_cw"      => $row[100] ?? null,
            "cd_salary_cx"      => $row[101] ?? null,
            "cd_salary_cy"      => $row[102] ?? null,
            "cd_salary_cz"      => $row[103] ?? null,

            "cd_salary_da"      => $row[104] ?? null,
            "cd_salary_db"      => $row[105] ?? null,
            "cd_salary_dc"      => $row[106] ?? null,
            "cd_salary_dd"      => $row[107] ?? null,
            "cd_salary_de"      => $row[108] ?? null,
            "cd_salary_df"      => $row[109] ?? null,
            "cd_salary_dg"      => $row[110] ?? null,
            "cd_salary_dh"      => $row[111] ?? null,
            "cd_salary_di"      => $row[112] ?? null,
            "cd_salary_dj"      => $row[113] ?? null,
            "cd_salary_dk"      => $row[114] ?? null,
            "cd_salary_dl"      => $row[115] ?? null,
            "cd_salary_dm"      => $row[116] ?? null,
            "cd_salary_dn"      => $row[117] ?? null,
            "cd_salary_do"      => $row[118] ?? null,
            "cd_salary_dp"      => $row[119] ?? null,
            "cd_salary_dq"      => $row[120] ?? null,
            "cd_salary_dr"      => $row[121] ?? null,
            "cd_salary_ds"      => $row[122] ?? null,
            "cd_salary_dt"      => $row[123] ?? null,
            "cd_salary_du"      => $row[124] ?? null,
            "cd_salary_dv"      => $row[125] ?? null,
            "cd_salary_dw"      => $row[126] ?? null,
            "cd_salary_dx"      => $row[127] ?? null,
            "cd_salary_dy"      => $row[128] ?? null,
            "cd_salary_dz"      => $row[129] ?? null,

            "cd_salary_ea"      => $row[130] ?? null,
            "cd_salary_eb"      => $row[131] ?? null,
            "cd_salary_ec"      => $row[132] ?? null,
            "cd_salary_ed"      => $row[133] ?? null,
            "cd_salary_ee"      => $row[134] ?? null,
            "cd_salary_ef"      => $row[135] ?? null,
            "cd_salary_eg"      => $row[136] ?? null,
            "cd_salary_eh"      => $row[137] ?? null,
            "cd_salary_ei"      => $row[138] ?? null,
            "cd_salary_ej"      => $row[139] ?? null,
            "cd_salary_ek"      => $row[140] ?? null,
            "cd_salary_el"      => $row[141] ?? null,
            "cd_salary_em"      => $row[142] ?? null,
            "cd_salary_en"      => $row[143] ?? null,
            "cd_salary_eo"      => $row[144] ?? null,
            "cd_salary_ep"      => $row[145] ?? null,
            "cd_salary_eq"      => $row[146] ?? null,
            "cd_salary_er"      => $row[147] ?? null,
            "cd_salary_es"      => $row[148] ?? null,
            "cd_salary_et"      => $row[149] ?? null,
            "cd_salary_eu"      => $row[150] ?? null,
            "cd_salary_ev"      => $row[151] ?? null,
            "cd_salary_ew"      => $row[152] ?? null,
            "cd_salary_ex"      => $row[153] ?? null,
            "cd_salary_ey"      => $row[154] ?? null,
            'pay_type'          => 0,
            "create_date"       => date('Y-m-d h:i:s'),
            'created_by'        => auth()->user()->admin_id
        ); 
    }

    function insertSalaryData($data_salary,$condition){
        if(!empty($data_salary)){
            foreach($data_salary as $data){
                $condition['cd_salary_a']   = $data['cd_salary_a'];
                $condition['cd_salary_bz']  = $data['cd_salary_bz'];
                $existSalary = DB::table('pay_salary_data')->where($condition)->first();
                if(!$existSalary){
                    DB::table('pay_salary_data')->insert($data);
                }
            }
        }
    }

    function insertNewSalaryData($data_salary,$condition){
        if(!empty($data_salary)){
            foreach($data_salary as $data){
                $condition['cd_salary_a']   = $data['cd_salary_a'];
                $existSalary = DB::table('pay_salary_data')->where($condition)->first();
                if(!$existSalary){
                    DB::table('pay_salary_data')->insert($data);
                }
            }
        }
    }

    public function checkDuplicateYearMonth(Request $request)
    {
        $year = $request->year;
        $month = $request->month;
        $salary_type = $request->salary_type;
        $pay_type = $request->pay_type;
        $_where = array(
            'cd_salary_type'    => $salary_type,
            'cd_salary_year'    => $year,
            'pay_type'          => $pay_type
        );
        if($salary_type==0){
            $_where['cd_salary_month'] =  $month;
        }
        $payslip_count = DB::table('pay_salary_data')->where($_where)->first();
        if($payslip_count){
            return 1;
        }else{
            return 0;
        }
    }

    //PAYSLIP FUNCTIONS
    public function showPayslipList(Request $request)
    {
        $cd_jdl_id              = $request->cd_jdl_id;
        $name                   = $request->cd_name;
        $mail                   = $request->cd_mail;
        $payment_date_year      = $request->payment_date_year;
        $payment_date_month     = $request->payment_date_month;
        $section_belong         = $request->cd_section_belong;
        $type_employee_payslip  = (int)$request->type_employee_payslip;
        $type_staff_payslip     = (int)$request->type_staff_payslip;

        $type_salary  = (int)$request->type_salary;
        $type_bonus   = (int)$request->type_bonus;

        $take = $request->input('length'); //limit
        $skip = $request->input('start'); //offset
        $payslip_array = array();
        $cd_salary_id_array = array();

        $jdl_id_login = 'all';
        $user_type  =  session()->get('s_user_type');
        if($user_type==2){
            $jdl_id_login = session()->get('c_jdl_id');
        }

        // Kiểm tra role 16 và 116 - chỉ hiển thị payslip của chính họ
        $user_roles = explode(',', session()->get('role', ''));
        $has_role_16_or_116 = in_array('16', $user_roles) && in_array('116', $user_roles);

        if($has_role_16_or_116){
            $jdl_id_login = session()->get('c_jdl_id');
            $user_type = 2; // Set user_type to 2 để áp dụng filter trong model
        }

        $pay_type = $request->pay_type;
        
        $payslip_list = PaylistModel::showPaylipList(1,$take,$skip,$cd_jdl_id,$name,$mail,$type_employee_payslip,$type_staff_payslip,$payment_date_year, $payment_date_month,$section_belong,$user_type, $jdl_id_login, $type_salary, $type_bonus, $pay_type);
        $payslip_list_count = PaylistModel::showPaylipList(0,$take,$skip,$cd_jdl_id,$name,$mail,$type_employee_payslip,$type_staff_payslip,$payment_date_year,$payment_date_month,$section_belong,$user_type, $jdl_id_login,$type_salary, $type_bonus, $pay_type);
        $total_payslip_list_count = PaylistModel::getTotalPayslipListCount($pay_type);
        foreach($payslip_list as $p)
        {
            
            $salary_bonus = '';
            $salary_month = $p['cd_salary_month'];
            if($p['cd_salary_type']==0){
                $salary_bonus = '[月例]';
                $salary_month = $salary_month."月";
            }else{
                $salary_bonus = '[賞与]'; 
                if($salary_month==1){
                    $salary_month = '①';
                }elseif($salary_month==2){
                    $salary_month = '➁';
                }
            }

            $web_section = '';
            
            if(!empty($pay_type)){
                    $web_section = $p["group_name"];
            }else{
                if($p['cd_salary_e'] === $p['cd_salary_g']){
                    $web_section = $p['cd_salary_e'];
                }else{
                    $web_section = $p['cd_salary_e'].$p['cd_salary_g']; 
                }
            }

            $c_kind = '';

            if(!empty($pay_type)){
                if($p["cd_salary_b"] == 100){
                    $c_kind = 'スタッフアカウント';
                }
                if($p["cd_salary_b"] == 1){
                    $c_kind = '従業員アカウント';
                }
            }else{
                if($p["cd_salary_bz"] == 100){
                    $c_kind = 'スタッフアカウント';
                }
                if($p["cd_salary_bz"] == 1){
                    $c_kind = '従業員アカウント';
                }
            }
            // if(!empty($pay_type)){
            //     $user_name = $p["user_name"];
            // }else{
            //     $user_name = $p["fullname"];
            // }
            $user_name = $p["user_name"];
            
            
            $btn_delete = '';
            // if($is_accountant=='true'){
            if(!empty($pay_type)){
                if(checkRoleUser(config("constants.ROLE.csv-format-import-edit.alias"))){
                    $btn_delete = '<button class="btn btn-sm btn-light btn_delete_payslip" data-cd_salary_id="'.$p["cd_salary_id"].'">削除</button>';
                }
            }else{
                if(checkRoleUser(config("constants.ROLE.csv-import-edit.alias"))){
                    $btn_delete = '<button class="btn btn-sm btn-light btn_delete_payslip" data-cd_salary_id="'.$p["cd_salary_id"].'">削除</button>';
                }
            }
            array_push($payslip_array,[
                'checkbox' => '<center><div class="custom-control custom-checkbox"><input type="checkbox" name="" class="custom-control-input check_row checkbox" value="'.$p["cd_salary_id"].'" id="check-'.$p["cd_salary_id"].'"><label class="custom-control-label"></label></div></center>',
                // 'checkbox' => $p["cd_salary_id"],
                'cd_jdl_id' => '<p style="">'.$p["cd_salary_a"].'</p>',
                'fullname' => '<p style="">'.$user_name.'</p>',
                'format_name' => '<p style="">'.(!empty($p["format_name"]) ? $p["format_name"]:'').'</p>',
                // 'c_mail' => '<p style="">'.$p["c_mail"].'</p>',
                // 'target_payment_date' => '<p style="">'.$p["target_payment_date"].'['.$web_type_text.']'.'</p>',
                'target_payment_date' => '<p style="">'.$p["cd_salary_year"]."年".$salary_month.$salary_bonus.'</p>',
                'section_belong' => '<p style="">'.$web_section.'</p>',
                'c_kind' => '<p style="">'.$c_kind.'</p>',// 1：正社員、100：スタッフ
                'action' => '<center><button class="btn btn-sm btn-light payslip_data mr-1" data-cd_salary_id="'.$p["cd_salary_id"].'">閲覧</button>'.$btn_delete.'</center>',
            ]);

            array_push($cd_salary_id_array,$p["cd_salary_id"]);
        }
        $request->session()->put([
            'cd_salary_values' => $cd_salary_id_array,
        ]);

        $json_data = array(
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => $total_payslip_list_count,
            "recordsFiltered" => $payslip_list_count,
            "data"            => $payslip_array,
        );
        echo json_encode($json_data);
    }

    public function getAllCheckboxId(Request $request)
    {
        $cd_jdl_id = $request->cd_jdl_id;
        $name = $request->cd_name;
        $mail = $request->cd_mail;
        $payment_date_year = $request->payment_date_year;
        $payment_date_month = $request->payment_date_month;
        $section_belong = $request->cd_section_belong;
        $type_employee_payslip  = (int)$request->type_employee_payslip;
        $type_staff_payslip     = (int)$request->type_staff_payslip;

        $type_salary  = (int)$request->type_salary;
        $type_bonus   = (int)$request->type_bonus;
        $pay_type   = (int)$request->pay_type;

        $checkbox_id_array = PaylistModel::getAllCheckboxId($cd_jdl_id,$name,$mail,$type_employee_payslip,$type_staff_payslip,$payment_date_year,$payment_date_month,$section_belong,$type_salary, $type_bonus, $pay_type);
        return $checkbox_id_array;
    }

    public function openPayslipModal(Request $request)
    {
        $cd_salary_id = $request->cd_salary_id;
        $pay_type = $request->pay_type;

        $pay_salary_data    = $this->getPayslipData($cd_salary_id);
        $view               = 'payslip_empty';
        $format_data = [];
        $get_user_info = [];
        if(!empty($pay_type)){
            $view = 'payslip-modal-content-by-format';
            $get_user_info = PaylistModel::get_document_user_info(!empty($cd_salary_id) ? $cd_salary_id : 0);
            $format_data = PaylistModel::get_document_approval_format_by_format_code(!empty($pay_salary_data['p_data']->cd_salary_d) ? $pay_salary_data['p_data']->cd_salary_d : 0);
        }else{
            $get_user_info = PaylistModel::get_document_user_info_main(!empty($cd_salary_id) ? $cd_salary_id : 0);

            if($pay_salary_data['p_data']->cd_salary_type == 1){
                $view = 'payslip-modal-content-bonus';
            }else{
                if($pay_salary_data['p_data']->cd_salary_bz == 100){
                    $view = 'payslip-modal-content';
                }else{
                    $view = 'payslip-modal-content-admin';
                }
            }
        }
        return view('Staff.Paylist.Includes.'.$view,[
            'p_data'        => $pay_salary_data['p_data'],//payslip_data
            't_data'        => $pay_salary_data['t_data'],//title_data,
            'format_title'  => $format_data,
            'user_info'     => $get_user_info,
            'listCompany'   => listCompany()
        ]);
    }

    public function openPayslipModalBatch(Request $request)
    {
        
        if(empty($request->checkbox_array))
        {
            $selected_payslip_id_array = $request->session()->get('cd_salary_values');
        }
        else
        {
            $selected_payslip_id_array = $request->checkbox_array;
        }

        $large_salary_array = array();

        foreach($selected_payslip_id_array as $cd_salary_id)
        {
            $pay_salary_data = $this->getPayslipData($cd_salary_id);
            $large_salary_array[$cd_salary_id] = $pay_salary_data;
        }
        $pay_type = $request->pay_type;

        $format_data = PaylistModel::get_document_approval_format();
        if(!empty($pay_type)){
            $all_user_info = PaylistModel::get_all_document_user_info();
        }else{
            $all_user_info = PaylistModel::get_all_document_user_info_main();
        }
        return view('Staff.Paylist.Includes.payslip-data-batch',[
            'large_salary_array' => $large_salary_array,
            'listCompany'   => listCompany(),
            'pay_type' => $pay_type,
            'list_format_title'  => !empty($format_data) ? json_decode(json_encode($format_data),true) : [],
            'list_user_info'  => !empty($all_user_info) ? json_decode(json_encode($all_user_info),true) : [],
        ]);
    }

    public function downloadPayslipPDF(Request $request)
    {
        $cd_salary_id = $request->cd_salary_id;
        $pay_salary_data = $this->getPayslipData($cd_salary_id);
        $filename = $pay_salary_data['p_data']->fullname.'.pdf';
        $path = storage_path() . '/'.  $filename;
       
        $view_file = 'payslip_empty';
        $pay_type = $request->pay_type;
        $format_data=[];
        $get_user_info = [];

        if(!empty($pay_type)){
            $view_file = 'payslip-data-tcpdf-by-format';
            $get_user_info = PaylistModel::get_document_user_info(!empty($cd_salary_id) ? $cd_salary_id : 0);
            $format_data = PaylistModel::get_document_approval_format_by_format_code(!empty($pay_salary_data['p_data']->cd_salary_d) ? $pay_salary_data['p_data']->cd_salary_d : 0);
        }else{
            $get_user_info = PaylistModel::get_document_user_info_main(!empty($cd_salary_id) ? $cd_salary_id : 0);
            if($pay_salary_data['p_data']->cd_salary_type==1){
                $view_file = 'payslip-data-tcpdf-bonus';
            }else{
                if($pay_salary_data['p_data']->cd_salary_bz == 100 ){
                    $view_file = 'payslip-data-tcpdf';
                }else{
                    $view_file = 'payslip-data-tcpdf-admin';
                }
            }
        }
        $view = \View::make('Staff.Paylist.Includes.'.$view_file,[
            'filename' => $filename,
            'p_data' => $pay_salary_data['p_data'],
            't_data' => $pay_salary_data['t_data'],
            'format_title'  => $format_data,
            'user_info'     => $get_user_info,
            'listCompany'   => listCompany()
        ]);

        $html_content = $view->render();

        TCPDF::SetTitle($filename);
        $fontname =  TCPDF_FONTS::addTTFfont(storage_path('fonts/ipag.ttf'), 'TrueTypeUnicode','', 32);
        TCPDF::SetFont($fontname , '', 12,'', false);
        //TCPDF::SetFont('msgothic', '', 12);
        TCPDF::SetAutoPageBreak(TRUE, 0);
        TCPDF::AddPage('L', 'A4');
        TCPDF::writeHTML($html_content, true, false, true, false, '');
        if (ob_get_level() > 0) {ob_end_clean();}

        TCPDF::Output($path, 'I');
        return response()->download(storage_path() . '/' . $filename)->deleteFileAfterSend(true);
    }

    public function downloadPayslipPDFBatch(Request $request)
    {
        $pay_type = $request->pay_type;

        if(empty($request->checkbox_array))
        {
            $selected_payslip_id_array = $request->session()->get('cd_salary_values');
        }
        else
        {
            $selected_payslip_id_array = explode(',',$request->checkbox_array);
        }

        foreach($selected_payslip_id_array as $index => $cd_salary_id)
        {
            $pay_salary_data = $this->getPayslipData($cd_salary_id);

            $filename = $pay_salary_data['p_data']->fullname.'.pdf';
            $path = storage_path() . '/'.  $filename;

            $user_type  =  session()->get('s_user_type'); 
            $view = 'payslip_empty';
            $format_data = [];
            $get_user_info = [];

            if(!empty($pay_type)){
                $view = 'payslip-data-tcpdf-by-format';
                $get_user_info = PaylistModel::get_document_user_info(!empty($cd_salary_id) ? $cd_salary_id : 0);
                $format_data = PaylistModel::get_document_approval_format_by_format_code(!empty($pay_salary_data['p_data']->cd_salary_d) ? $pay_salary_data['p_data']->cd_salary_d : 0);
            }else{
                $get_user_info = PaylistModel::get_document_user_info_main(!empty($cd_salary_id) ? $cd_salary_id : 0);

                if($pay_salary_data['p_data']->cd_salary_type==1){
                    $view = 'payslip-data-tcpdf-bonus';
                }else{
                    if($pay_salary_data['p_data']->cd_salary_bz == 100 ){
                        $view = 'payslip-data-tcpdf';
                    }else{
                        $view = 'payslip-data-tcpdf-admin';
                    }
                }
            }
            $view = \View::make('Staff.Paylist.Includes.'.$view,[
                'filename' => $filename,
                'p_data' => $pay_salary_data['p_data'],
                't_data' => $pay_salary_data['t_data'],
                'format_title'  => $format_data,
                'user_info'     => $get_user_info,
                'listCompany'   => listCompany()
            ]);
            $html_content = $view->render();

            TCPDF::SetTitle($filename);
            $fontname =  TCPDF_FONTS::addTTFfont(storage_path('fonts/ipag.ttf'), 'TrueTypeUnicode','', 32);
            TCPDF::SetFont($fontname , '', 12,'', false);
            //TCPDF::SetFont('msgothic', '', 12);
            TCPDF::SetAutoPageBreak(TRUE, 0);
            TCPDF::AddPage('L', 'A4');
            TCPDF::writeHTML($html_content, true, false, true, false, '');
        }
        if (ob_get_level() > 0) {ob_end_clean();}

        TCPDF::Output($path, 'I');
        return response()->download(storage_path() . '/' . $filename)->deleteFileAfterSend(true);
    }

    public function getPayslipData($cd_salary_id=0)
    {
        
        $payslip_data   = PaylistModel::getPayslipModalData($cd_salary_id);

        $zero_append_count = 4 - strlen($payslip_data->cd_salary_d);
        if($zero_append_count>0)
        {
            $payslip_data->cd_salary_belongc = str_repeat('0',$zero_append_count).$payslip_data->cd_salary_d;
        }

        $cd_salary_type = !empty($payslip_data->cd_salary_type) ? $payslip_data->cd_salary_type : 0;
        $title_data = new stdClass;
        $title_data->work_title = $cd_salary_type ? '賞与' : '給料';
        $title_data->d_worktime8 = $cd_salary_type ? '' : '時間';
        $title_data->m_worktime8 = $cd_salary_type ? '' : '単価金額';

        return array(
            'p_data'    => $payslip_data,
            't_data'    => $title_data
        );
    }

    function deletePayslip(Request $req){
        $arr_payslip_id = explode(",",$req->arr_payslip_id);
        $status = 'error';
        $pay_type = $req->pay_type;

        try {
            /* Lưu lại dữ liệu đã bị xóa */
            $activityLog ['name_action'] = 'delete_salary';
            if(!empty($arr_payslip_id)){
                $obj_action = $old_value = [];
                foreach($arr_payslip_id as $salary_id){
                    $obj_action[] = $salary_id;
                    $old_value[$salary_id] = PaylistModel::getSalaryData($salary_id);
                }
                $old_value['pay_type']     = $pay_type;
                $activityLog['obj_action'] = implode(',',$obj_action);
                $activityLog['old_value']  = json_encode($old_value);
                $activityLog['new_value']  = null;
            }
            DB::beginTransaction();
            if(!empty($pay_type)){
                if(checkRoleUser(config("constants.ROLE.csv-format-import-edit.alias")) && PaylistModel::deletePayslip($arr_payslip_id)){
                    Helper::activityLog($activityLog);
                    $status = 'success';
                }
            }else{
                if(checkRoleUser(config("constants.ROLE.csv-import-edit.alias")) && PaylistModel::deletePayslip($arr_payslip_id)){
                    Helper::activityLog($activityLog);
                    $status = 'success';
                }
            }
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            $msg = $e->getMessage();
            $status = 'error';
        }
        return response()->json(['status' => $status, 'msg' => $msg?? ""]); 
    }

    //EMPLOYEE MASTER FUNCTIONS
    public function showEmployeeMasterList(Request $request)
    {
        $c_jdl_id = (int)$request->c_jdl_id;
        $name = $request->name;
        $mail = $request->mail;
        $section_belong_c = $request->section_belong;
        $type_employee = (int)$request->type_employee;
        $type_staff = (int)$request->type_staff;
        $email_registered = (int)$request->email_registered;
        $email_not_registered = (int)$request->email_not_registered;
        $take = $request->input('length'); //limit
        $skip = $request->input('start'); //offset
        $employee_master_array = [];

        $employee_master_list = PaylistModel::showEmployeeMasterList(1,$take,$skip,$c_jdl_id,$name,$mail,$type_employee,$type_staff,$email_registered,$email_not_registered,$section_belong_c);
        $employee_master_list_count = PaylistModel::showEmployeeMasterList(0,$take,$skip,$c_jdl_id,$name,$mail,$type_employee,$type_staff,$email_registered,$email_not_registered,$section_belong_c);
        $total_employee_master_list_count = PaylistModel::getTotalEmployeeMasterListCount();

        foreach($employee_master_list as $e)
        {
            // if(!empty(trim($e["c_mail"]))&&!empty(trim($e["c_pass"])))//if not empty
            // {
            //     $effectiveness_html = '<p style="margin-left:18px;">有効</p>';
            // }
            // else
            // {
                if($e["c_jdl_id"])//if not empty
                {
                    $effectiveness_html = '<p style="margin-left:18px;">有</p>';
                }
                else
                {
                    $effectiveness_html = '<p style="margin-left:18px;">無</p>';
                }
            // }

            array_push($employee_master_array,[
                'c_jdl_id' => '<p style="margin-left:18px;">'.$e["c_jdl_id"].'</p>',
                'fullname' => '<p style="margin-left:18px;">'.$e["fullname"].'</p>',
                // 'c_mail' => '<div class="input-group input-group-sm input-note">
                //                 <input type="text" class="form-control email_textbox" data-c_emp_id="'.$e["c_emp_id"].'" data-original_email="'.$e["c_mail"].'" value="'.$e["c_mail"].'">
                //                 <div class="input-group-append">
                //                     <button class="input-group-text btn-change-email">
                //                         <i class="fas fa-edit fa-lg color-primary"></i>
                //                     </button>
                //                 </div>
                //             </div>',
                // 'c_mail' => '<span>'.$e["c_mail"].'</span>',
                'section_belong' => '<p style="margin-left:18px;">'.$e["section_belong"].'</p>',
                'c_kind' => '<p style="margin-left:18px;">'.($e["c_kind"] == 100 ? ' スタッフ' : ' 正社員').'</p>',// 1：正社員、100：スタッフ
                'effectiveness' => $effectiveness_html,
                // 'action' => '<center><button class="btn btn-sm btn-light emp_master_data" data-c_emp_id="'.$e["c_emp_id"].'">閲覧</button></center>',
            ]);
        }
        $json_data = array(
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => $total_employee_master_list_count,
            "recordsFiltered" => $employee_master_list_count,
            "data"            => $employee_master_array
            );

        echo json_encode($json_data);
    }

    public function openEmployeeMasterModal(Request $request)
    {
        $c_emp_id = $request->c_emp_id;

        $employee_master_data = PaylistModel::getEmployeeMasterData($c_emp_id);

        return view('Staff.Paylist.Includes.employee-master-modal',[
            'emp_data' => $employee_master_data,
        ]);
    }

    public function openEmployeeMasterConfirmationModal(Request $request)
    {
        $name = $request->name;
        $c_mail = $request->c_mail;
        $temporary_password = $request->temporary_password;
        $is_active = (int)$request->is_active;
        $is_registered = (int)$request->is_registered;
        $to_enable = (int)$request->to_enable;
        $c_emp_id = $request->c_emp_id;

        if($is_active)//閲覧許可
        {
            $viewing_permission = "許可する";
        }
        else
        {
            $viewing_permission = "許可しない";
        }

        $c_belong = PaylistModel::getEmployeeMasterData($c_emp_id)->c_belong;

        if($c_belong=="スタッフ")
        {
            $pt_staff = PaylistModel::checkEmailFromPtStaff($c_mail);
            if(!empty($pt_staff))
            {
                $staff_id = $pt_staff->staff_id;
                $returned_c_emp_id = PaylistModel::checkStaffIdIfExists($staff_id);//for unique staff id constraint in pay_employee
                if(!empty($returned_c_emp_id))
                {
                    $returned_c_emp_id = $returned_c_emp_id->c_emp_id;
                }
                if($returned_c_emp_id != $c_emp_id && !empty($returned_c_emp_id))//if staff id is present in another row of pay_employee table
                {
                    return "fail2";
                }
                else
                {
                    return view('Staff.Paylist.Includes.employee-master-confirmation-modal',[
                        'name' => $name,
                        'c_mail' => $c_mail,
                        'temporary_password' => $temporary_password,
                        'viewing_permission' => $viewing_permission,
                        'is_active' => $is_active,
                        'is_registered' => $is_registered,
                        'to_enable' => $to_enable,
                        'c_emp_id' => $c_emp_id,
                        'staff_id' => $staff_id,
                    ]);
                }
            }
            else
            {
                return "fail";
            }
        }
        else//can change password without fetching the staff id
        {
            return view('Staff.Paylist.Includes.employee-master-confirmation-modal',[
                'name' => $name,
                'c_mail' => $c_mail,
                'temporary_password' => $temporary_password,
                'viewing_permission' => $viewing_permission,
                'is_active' => $is_active,
                'is_registered' => $is_registered,
                'to_enable' => $to_enable,
                'c_emp_id' => $c_emp_id,
                'staff_id' => 0,
            ]);
        }
    }

    public function editEmployeeMasterData(Request $request)
    {
        $c_emp_id = $request->c_emp_id;
        $c_mail = $request->c_mail;
        $staff_id = $request->staff_id;
        $temporary_password = $request->temporary_password;
        $viewing_permission = $request->viewing_permission;
        $is_active = $request->is_active;
        $to_enable = (int)$request->to_enable;

        $employee_master_data = PaylistModel::getEmployeeMasterData($c_emp_id);

        if($to_enable)//if purpose is to register an email with temporary password
        {
            $email_data = array(
                'email_sent_date' => date('Y-m-d H:i:s'),
                'fullname' => $employee_master_data->fullname,
                'c_mail' => $c_mail,
                'viewing_permission' => $viewing_permission,
                'temporary_password' => $temporary_password,
            );

            $email_recipient = $c_mail;
            $email_subject = "[給与明細システム]登録完了報告メール";
            // Mail::send("Staff.Paylist.Includes.employee-master-email-content", $email_data, function($message) use ($email_recipient,$email_subject) {
            //     $message->to($email_recipient, $email_recipient);
            //     $message->subject($email_subject);
            // });

            try{
                Mail::send("Staff.Paylist.Includes.employee-master-email-content", $email_data, function($message) use ($email_recipient,$email_subject) {
                    $message->to($email_recipient, $email_recipient);
                    $message->subject($email_subject);
                });
            }
            catch(\Exception $e){
                return $e->getMessage();
                // Get error here
            }

            if (Mail::failures()) {
                // return 1;
                $result = false;
            }
            else
            {
                $result = true;
            }
        }

        $result = PaylistModel::editEmployeeMasterData($c_emp_id,$c_mail,$staff_id,$is_active,$temporary_password);

        return $result ? 'success' : 'fail';
    }

    public function openRegisterEmailModal(Request $request)
    {
        $c_emp_id = $request->c_emp_id;
        $current_email = $request->current_email;

        $email_exists = PaylistModel::checkEmailIfExists($current_email);

        $pt_staff = PaylistModel::checkEmailFromPtStaff($current_email);

        if(!empty($pt_staff))
        {
            $staff_id = $pt_staff->staff_id;

            if(!$email_exists && !empty($pt_staff))
            {
                $cant_register = 0;
            }
            else
            {
                $cant_register = 1;
            }

            $employee_master_data = PaylistModel::getEmployeeMasterData($c_emp_id);

            return view('Staff.Paylist.Includes.employee-master-register-email-modal',[
                'c_emp_id' => $c_emp_id,
                'current_email' => $current_email,
                'staff_id' => $staff_id,
                'cant_register' => $cant_register,
                'emp_data' => $employee_master_data,
            ]);
        }
        else
        {
            return "no pt staff email";
        }
    }

    public function registerEmployeeMasterEmail(Request $request)
    {
        $c_emp_id = (int)$request->c_emp_id;
        $c_mail = $request->c_mail;
        $staff_id = $request->staff_id;

        $result = PaylistModel::editEmployeeMasterDataEmail($c_emp_id,$c_mail,$staff_id);
        return $result ? 'success' : 'fail';
    }

    public function exportEmployerCSV(Request $request)
    {
        $form_year = $request->form_year;
        $form_month = $request->form_month;
        $end_year = $request->end_year;
        $end_month = $request->end_month;
        $data = $this->queryExportEmployer($form_year, $form_month, $end_year, $end_month);
        
        try {
            set_time_limit(0);
            ini_set('memory_limit', '2048M');

            $objPHPExcel = $this->exportData($data, $end_year, $end_month);
            $prefixFileName = "新ポータルスタッフデータテンプレート-";
            $date = date('Y_m_d');
            $filename = "$prefixFileName$date.csv";

            if (ob_get_level() > 0) {
                ob_end_clean();
            }
        
            header('Content-Encoding: UTF-8');
            header("Content-Type: application/vnd.ms-excel");
            header("Content-Disposition: attachment; filename=\"$filename\"");
            $objWriter = new Csv($objPHPExcel);
            $objWriter->setUseBOM(true);
            ob_start();
            $objWriter->save("php://output");
            $xlsData = ob_get_contents();
            ob_end_clean();
            $res = [
                'success' => true,
                'message' => 'Export compete.',
                'filename' => $filename,
                'file' => "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; base64,".base64_encode($xlsData) //mime type of used format
            ];
            return Response::json($res, 200);
        } catch (\Throwable $th) {
            return Response::json([
                'success' => false,
                'message' => 'Export fails.'
            ], 500);
        }
    }

    public function exportEmployerCSV2(Request $request)
    {
        $form_year = $request->form_year;
        $form_month = $request->form_month;
        $end_year = $request->end_year;
        $end_month = $request->end_month;
        //$data = $this->queryExportEmployer($form_year, $form_month, $end_year, $end_month);
        $data = $this->queryExportStaff($form_year, $form_month, $end_year, $end_month);
        try {
            set_time_limit(0);
            ini_set('memory_limit', '2048M');
            //$objPHPExcel = $this->exportData2($data);
            $objPHPExcel = $this->exportDataNew($data);
            $prefixFileName = "社員コード更新";
            $filename = "$prefixFileName.csv";

            if (ob_get_level() > 0) {
                ob_end_clean();
            }
        
            header('Content-Encoding: UTF-8');
            header("Content-Type: application/vnd.ms-excel");
            header("Content-Disposition: attachment; filename=\"$filename\"");
            $objWriter = new Csv($objPHPExcel);
            $objWriter->setUseBOM(true);
            ob_start();
            $objWriter->save("php://output");
            $xlsData = ob_get_contents();
            ob_end_clean();
            $res = [
                'success' => true,
                'message' => 'Export compete.',
                'filename' => $filename,
                'file' => "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; base64,".base64_encode($xlsData) //mime type of used format
            ];
            return Response::json($res, 200);
        } catch (\Throwable $th) {
            return Response::json([
                'success' => false,
                'message' => 'Export fails.'
            ], 500);
        }
    }

    public function queryExportStaff($form_year, $form_month, $end_year, $end_month)
    {
        $contractDestroy = DB::table('pt_staff_contract_destroy as ce')
            ->join('pt_staff_contract_detail as cd', 'cd.id', 'ce.contract_detail_id')
            ->where('ce.user_route_id', '=', 1)
            ->whereIn('cd.status', [10,11])
            ->select('ce.contract_detail_id', 'cd.staff_work_pattern_id', DB::raw('max(ce.created_at) as created_at'))
            ->whereNull('delete_date')
            ->groupBy('ce.contract_detail_id', 'cd.staff_work_pattern_id');

        return DB::table('pt_staff as ps')
            ->join('staff_work_pattern as swp', 'ps.staff_id', 'swp.staff_id')
            ->join('staff_work_report as swr', 'swr.pattern_id', 'swp.id')
            ->leftJoinSub($contractDestroy, 'cd', function ($join) {
                $join->on('cd.staff_work_pattern_id', '=', 'swr.pattern_id');
            })
            ->leftJoin('admin_bank_account as abc', 'ps.staff_id', 'abc.admin_id')
            ->whereNull('swp.delete_date')
            ->whereNull('swr.delete_date')
            ->whereNull('ps.delete_date')
            ->whereNull('cd.created_at')
            ->where('swr.is_hidden_from_search', false)
            ->whereBetween('swr.year', [$form_year,$end_year])
            ->whereBetween('swr.month', [$form_month,$end_month])
            ->select(
                'ps.staff_id',
                'ps.jdl_id',
                'ps.name as staff_name', 
                'ps.birth', 
                'swr.year', 
                'swr.month'
            )
            ->distinct()->get();
    }

    public function queryExportEmployer($form_year, $form_month, $end_year, $end_month)
    {
        $contractDestroy = DB::table('pt_staff_contract_destroy as ce')
            ->join('pt_staff_contract_detail as cd', 'cd.id', 'ce.contract_detail_id')
            ->where('ce.user_route_id', '=', 1)
            ->whereIn('cd.status', [10,11])
            ->select('ce.contract_detail_id', 'cd.staff_work_pattern_id', DB::raw('max(ce.created_at) as created_at'))
            ->whereNull('delete_date')
            ->groupBy('ce.contract_detail_id', 'cd.staff_work_pattern_id');

        return DB::table('pt_staff as ps')
            ->join('staff_work_pattern as swp', 'ps.staff_id', 'swp.staff_id')
            ->join('staff_work_report as swr', 'swr.pattern_id', 'swp.id')
            ->leftJoinSub($contractDestroy, 'cd', function ($join) {
                $join->on('cd.staff_work_pattern_id', '=', 'swr.pattern_id');
            })
            ->leftJoin('admin_bank_account as abc', 'ps.staff_id', 'abc.admin_id')
            ->whereNull('swp.delete_date')
            ->whereNull('swr.delete_date')
            ->whereNull('cd.created_at')
            ->where('swr.is_hidden_from_search', false)
            ->whereBetween('swr.year', [$form_year,$end_year])
            ->whereBetween('swr.month', [$form_month,$end_month])
            ->select(
                'ps.staff_id',
                'ps.jdl_id',
                'ps.name as staff_name', 
                'ps.kana as kana_name', 
                'ps.sex', 
                'ps.birth', 
                'ps.tel', 
                'ps.tel_mobile', 
                'ps.zip',
                'ps.pref',
                'ps.addr1',
                'ps.addr2',
                'abc.main_bank_id',
                'abc.bank_branch_id',
                'abc.account_number',
                'swr.year', 
                'swr.month'
            )
            ->groupBy(
                'ps.staff_id',
                'ps.jdl_id',
                'ps.name', 
                'ps.kana', 
                'ps.sex', 
                'ps.birth', 
                'ps.tel', 
                'ps.tel_mobile', 
                'ps.zip',
                'ps.pref',
                'ps.addr1',
                'ps.addr2',
                'abc.main_bank_id',
                'abc.bank_branch_id',
                'abc.account_number',
                'swr.year', 
                'swr.month'
            )
            ->get();
    }

    public function exportData($data, $end_year, $end_month)
    {
        $path = storage_path('template/portal_staff_template.xlsx');         
       
        $objPHPExcel = \PhpOffice\PhpSpreadsheet\IOFactory::load($path);
        $data_json = file_get_contents(public_path() . "/json_data_bank/banks.json");
        $lisk_bank = json_decode($data_json, true);

        $i = 2;
        foreach ($data as $row) {
            $furigana = preg_split("/( )|(　)|(　)/",$row->staff_name);
            $firts_furigana = implode("",array_splice($furigana, 0, 1));
            $last_furigana = implode("",$furigana);
            $kana = preg_split("/( )|(　)|(　)/",$row->kana_name);
            $firts_kana = implode("",array_splice($kana, 0, 1));
            $last_kana = implode("",$kana);

            $zip = explode("-",$row->zip);
            
            $bank = "";
            if ($row->main_bank_id) {
                $list_bank = $lisk_bank[ str_pad($row->main_bank_id,4,"0",STR_PAD_LEFT)];
                $bank = $list_bank['name']??'';
            }
            
            $branches = "";
            if ($row->bank_branch_id) {
                $url = config("constants.URL_GET_BANK_BRANCH"). str_pad($row->main_bank_id,4,"0",STR_PAD_LEFT) ."/branches/search.json?code=".$row->bank_branch_id;
                $list_branch = $this->useCurl($url);
                $branches = $list_branch[0]['name']??'';
            }
            $phone = $row->tel_mobile ?? ($row->tel ??  '' );
            
            $objPHPExcel->getActiveSheet(1)
                ->setCellValue('A' . ($i), $row->jdl_id)
                ->setCellValue('B' . ($i), $firts_furigana)
                ->setCellValue('C' . ($i), $last_furigana)
                ->setCellValue('D' . ($i), $firts_kana)
                ->setCellValue('E' . ($i), $last_kana)
                ->setCellValue('F' . ($i), $zip[0] ? '=TEXT(' . $zip[0] .', "000")' : "")
                ->setCellValue('G' . ($i), isset($zip[1]) ? '=TEXT(' . $zip[1] . ', "0000")' : "")
                ->setCellValue('H' . ($i), $row->pref.$row->addr1)
                ->setCellValue('I' . ($i), $row->addr2)
                ->setCellValue('J' . ($i), $phone)
                ->setCellValue('K' . ($i), date('Y/m/d', strtotime($row->birth))??"")
                ->setCellValue('L' . ($i), $row->sex)
                ->setCellValue('M' . ($i), $bank)
                ->setCellValue('N' . ($i), $branches)
                ->setCellValue('O' . ($i), "普通")
                ->setCellValue('P' . ($i), $row->account_number??"")
                ->setCellValue('Q' . ($i), $row->staff_id??"");
            $objPHPExcel->getActiveSheet(1)->getCell("F" . ($i))->getStyle()->setQuotePrefix(true); 
            $objPHPExcel->getActiveSheet(1)->getCell("G" . ($i))->getStyle()->setQuotePrefix(true); 
            $objPHPExcel->getActiveSheet(1)->getCell("J" . ($i))->getStyle()->setQuotePrefix(true); 
            // $objPHPExcel->getActiveSheet(1)->getStyle('J')->getNumberFormat()->setFormatCode('000-0000-0000');

            $i++;
        } 
        return $objPHPExcel;
    }

    public function exportDataNew($data)
    {
        $i = 1;
        $spreadsheet = new Spreadsheet();
        $spreadsheet->getActiveSheet(1)
                    ->setCellValue('A' . ($i), '社員ｺｰﾄﾞ')
                    ->setCellValue('B' . ($i), '氏名')
                    ->setCellValue('C' . ($i), '生年月日')
                    ->setCellValue('D' . ($i), '世帯主コード');
        $i++;
        foreach ($data as $row) {
            // $furigana       = preg_split("/( )|(　)|(　)/",$row->staff_name);
            // $firts_furigana = implode("",array_splice($furigana, 0, 1));
            // $last_furigana  = implode("",$furigana);
            $spreadsheet->getActiveSheet(1)
                        ->setCellValue('A' . ($i), $row->jdl_id)
                        ->setCellValue('B' . ($i), $row->staff_name)
                        ->setCellValue('C' . ($i), " " . date('Y/m/d', strtotime($row->birth))??"")
                        ->setCellValue('D' . ($i), $row->staff_id);
            $i++;
        } 
        return $spreadsheet;
    }

    public function exportData2($data)
    {
        $i = 1;
        $spreadsheet = new Spreadsheet();
        $spreadsheet->getActiveSheet(1)
                    ->setCellValue('A' . ($i), '社員ｺｰﾄﾞ')
                    ->setCellValue('B' . ($i), '氏名')
                    ->setCellValue('C' . ($i), '生年月日');
        $i++;
        foreach ($data as $row) {
            $furigana       = preg_split("/( )|(　)|(　)/",$row->staff_name);
            $firts_furigana = implode("",array_splice($furigana, 0, 1));
            $last_furigana  = implode("",$furigana);
            $spreadsheet->getActiveSheet(1)
                        ->setCellValue('A' . ($i), $row->jdl_id)
                        ->setCellValue('B' . ($i), $firts_furigana.$last_furigana)
                        ->setCellValue('C' . ($i), " " . date('Y/m/d', strtotime($row->birth))??"");
            $i++;
        } 
        return $spreadsheet;
    }

    private function useCurl($url)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        $content = curl_exec($ch);
        curl_close($ch);
        return json_decode($content, true);
    }

    public function importEmployerCode(VALIDATOR_FILE $request)
    {
        $BEGIN_ROW = 1;
        $TEMP_PATH = 'storage/app/public/';
        $path = $request->file('csv_file')->store('tmp');

        if ($path) {
            $pathDir = base_path($TEMP_PATH . $path);
            $objPHPExcel = \PhpOffice\PhpSpreadsheet\IOFactory::load($pathDir);

            $excelArr = $objPHPExcel->getActiveSheet(1)->toArray();
            $excelArr = array_slice($excelArr, $BEGIN_ROW);

            // Delete file temp
            if (file_exists($pathDir)) {
                chmod($pathDir, 0755);
                unlink($pathDir);
            }
            $listErrors = [];
            $listUpdate = [];
            try {
                $listUpdate = [];
                $lstImportItem = [];
                $lstImportName = [];
                $lstImportBirth = [];
                foreach ($excelArr as $key => $value) {
                    $value[3] = $key + $BEGIN_ROW + 1;
                    $itemImport             = [];
                    $itemImportName         = [];
                    $itemImportBirth        = [];

                    $itemImport['jdl_id']           = $value[0];
                    $itemImport['name']             = $value[1];
                    $itemImport['birth']            = date('Y/m/d', strtotime($value[2]));
                    
                    $itemImportName[]               = $value[1];
                    $itemImportBirth[]              = date('Y/m/d', strtotime($value[2]));


                    $lstImportName[]        = $itemImportName;
                    $lstImportBirth[]       = $itemImportBirth;
                    $lstImportItem[]        = $itemImport;
                }
                $listErrors = $this->checkRowNew($lstImportItem, $lstImportName, $lstImportBirth, $listUpdate);

                if (empty($listErrors) && count($listUpdate)> 0) {
                    $this->importData($listUpdate);
                }

                if ($listErrors) {
                    return Response::json([
                        'success' => false,
                        'message' => $listErrors,
                    ], 422);
                } else {
                    return Response::json([
                        'success' => true,
                        'message' => 'インポートが完了しました。'
                    ], 200);
                }
            } catch (\Throwable $th) {
                return Response::json([
                    'success' => false,
                    'message' => 'エラー',
                ], 500);
            }
        }
        return Response::json([
            'success' => false,
            'message' => '選択されていません.'
        ], 500);
    }
    
    private function checkRow($row, $listStaff)
    {
        $isUserExists = false;
        $isCodeExists = false;
        $listErr = [];
        foreach ($listStaff as $key => $value) {
            $birth = Carbon::createFromFormat('Y-m-d', date('Y-m-d', strtotime($value->birth)));
            $birthImp = Carbon::createFromFormat('Y-m-d', date('Y-m-d', strtotime($row[2])));
            if ($value->staff_name == $row[1] && $birth->eq($birthImp)) {
                $isUserExists = true;
            }
            if ($row[0] && $value->jdl_id == $row[0]) {
                $isCodeExists = true;
            }
        }
        if (!$isUserExists) {
            $listErr[] = "ユーザーがいないため、更新できませんでした". $row[3] ."行目"; // khong tim thay thong tin staff
        }
        if ($isCodeExists) {
            $listErr[] = "既に存在する社員コードです". $row[3] ."行目"; // ma nhan vien ton tai
        }
        return $listErr;
    }

    private function listStaff()
    {
        return DB::table('pt_staff as ps')
            ->groupBy('ps.jdl_id', 'ps.birth', 'ps.name')
            ->select(
                'ps.jdl_id',
                'ps.birth',
                'ps.name as staff_name'
            )
            ->whereNotNull('ps.birth')
            ->whereNotNull('ps.name')
            ->get()
            ->toArray();
    }
    private function importPtStaff($lstImportName, $lstImportBirth)
    {
        return ImportEmployerCodeModel::whereNotNull('birth')
                                        ->whereNotNull('name')
                                        //->where('registration', 2) //仮登録のみ
                                        ->whereIn('name', $lstImportName)
                                        ->whereNull('delete_date')
                                        // ->whereIn('birth', $lstImportBirth)
                                        ->whereExists(function($query) {
                                            $query->select(DB::raw(1))
                                                ->from('pt_staff_support_history')
                                                ->whereColumn('pt_staff_support_history.staff_id', 'pt_staff.staff_id')
                                                ->where('state', '!=', 11)
                                                ->orderBy('update_date', 'DESC')
                                                ->limit(1);
                                        })
                                        ->groupBy('staff_id', 'jdl_id', 'birth', 'name')
                                        ->select(
                                            'staff_id',
                                            'jdl_id',
                                            'birth',
                                            'name as staff_name',
                                            'type_staff',
                                            'registration'
                                        )
                                        ->orderBy('create_date', 'DESC')
                                        ->get();
    }

    private function checkRowNew($lstImportItem, $lstImportName, $lstImportBirth, &$listUpdate)
    {
        $listErr = [];
        $isUserExists = false;
        $isCodeExists = false;
        $index = 2;
        $temp = '';
        $listStaff = $this->importPtStaff($lstImportName, $lstImportBirth);

        $listStaff = $listStaff->groupBy('staff_name')->map(function ($group) {
            return $group->sortByDesc('create_date')->first();
        })->values();


        foreach ($lstImportItem as $keyImport => $valueImport) {
            if (count($listStaff) == 0) {
                $isUserExists = false;
            }
            foreach ($listStaff as $keyDb => $valueDb) {
                $birth = Carbon::createFromFormat('Y-m-d', date('Y-m-d', strtotime($valueDb->birth)));
                $birthImp = Carbon::createFromFormat('Y-m-d', date('Y-m-d', strtotime($valueImport['birth'])));
                // nhan vien dang ky chinh thuc va dang ky tam tru deu cho cap nhat
                // if($valueDb->type_staff == 0 && $valueDb->registration != 2) 
                // {
                //     $listErr[] = "仮登録スタッフはコードが更新できません。". $index ."行目"; // nhan vien 仮登録 =1
                // }
                if ($valueImport['name'] == $valueDb->staff_name && $birth->eq($birthImp)) {
                    $isUserExists = true;
                    $itemImport = new ImportEmployerCodeModel();
                    $itemImport->staff_id   = $valueDb->staff_id;
                    $itemImport->jdl_id     = $valueImport['jdl_id'];
                    $itemImport->name       = $valueImport['name'];
                    $itemImport->birth      = $valueImport['birth'];
                    $itemImport->old_jdl_id = $valueDb->jdl_id;
                    $listUpdate[] = $itemImport;

                    unset($listStaff[$keyDb]);
                    break;
                } elseif ($valueImport['jdl_id']  == $valueDb->jdl_id) {
                    $isCodeExists = true;
                    break;
                }
            }
            
            if (!$isUserExists) {
                $listErr[] = "ユーザーがいないため、更新できませんでした". $index ."行目"; // khong tim thay thong tin staff
            }
           
            if ($isCodeExists) {
                $listErr[] = "既に存在する社員コードです". $index ."行目"; // ma nhan vien ton tai
            }

            $check_jdl_id_exist = ImportEmployerCodeModel::check_staff_jdl_no((string)$valueImport['jdl_id']);
            if($check_jdl_id_exist > 0){
                $listErr[] = "社員コードはすでに存在しています。". $index ."行目"; // ma nhan vien ton tai
            }
 
            $index = $index + 1;
        }
        return $listErr;
    }

    private function importData($listUpdate)
    {
        foreach ($listUpdate as $key => $value) {
            ImportEmployerCodeModel::where('staff_id', $value->staff_id)
            ->whereNull('delete_date')
            ->update(
                [
                    'jdl_id'=>$value->jdl_id
                ]
            );
            DB::table('pay_salary_data')->where('cd_salary_a', $value->old_jdl_id)->whereNull('delete_date')->update(['cd_salary_a' => $value->jdl_id]);
        }
    }
    public function indexNewStaffPayList(){
        $request = new Request();
        $request->new_staff_paylist = 1;
        return $this->show($request);
    }

    public function storePayslipFormat(Request $request){
        $format_data  = $request->format_data;
        DB::beginTransaction();
        try {
            $id                 = (int)$format_data['id']??0;
            $format_id          = (int)$format_data['format_id'];
            $current_format_id  = (int)$format_data['current_format_id'];
            $isError = false;
            if($id==0){
                $check_exist_format_id = DB::table('payslip_format_header')->whereNull('delete_date')->whereNotNull('format_id')->where('format_id',$format_data['format_id'])->first();
                if($check_exist_format_id){
                    $isError  = true;
                }
                $sort = PaylistModel::get_last_format_sort();
                $format_data['sort'] = $sort;
                $format_data['value'] = !empty($format_data['items']) ? json_encode($format_data['items']) : null;
                unset($format_data['items']);
                unset($format_data['id']);
                unset($format_data['current_format_id']);
                $format_data['format_id'] = $format_id > 0 ? $format_id : null;
                $format_data['creator_admin_id']    = Auth::user()->admin_id;
                $format_data['create_date']         = date("Y-m-d H:i:s");
                DB::table('payslip_format_header')->insert($format_data);
            }else{
                if($format_id != $current_format_id){
                    $check_exist_format_id = DB::table('payslip_format_header')->whereNotNull('format_id')->whereNull('delete_date')->where('format_id',$format_data['format_id'])->where('id','<>', $id)->first();
                    if($check_exist_format_id){
                        $isError  = true;
                    }
                }
                $format_data['value'] = !empty($format_data['items']) ? json_encode($format_data['items']) : null;
                $format_data['format_id'] = $format_id > 0 ? $format_id : null;
                unset($format_data['items']);
                unset($format_data['id']);
                unset($format_data['current_format_id']);
                DB::table('payslip_format_header')->where('id',$id)->update($format_data);
            }

            if($isError){
                DB::rollBack();
                return Response::json([
                    'success' => false,
                    'message' => 'フォーマットIDが存在します',
                ], 500);
            }else{
                DB::commit();
                return Response::json([
                    'success' => true,
                    'message' => 'インポートが完了しました。'
                ], 201);
            }
        } catch (\Throwable $th) {
            DB::rollBack();
            return Response::json([
                'success' => false,
                'message' => 'エラー',
            ], 500);
       }
       return Response::json([
           'success' => false,
           'message' => '選択されていません.'
       ], 500);
    }

    public function save_payslip_format_sorting(Request $request) {
        $format_list = $request->format_list;
        if (!empty($format_list)) {
            foreach ($format_list as $key => $value) {
                PaylistModel::update_data($value,'payslip_format_header', 'id', $value['id']);
            }
        }
        return 'success';
    }

    public function destroy( Request $request )
    {
        $delete = PaylistModel::deletion($request->id);
        return redirect()->back()->with('success', $delete[0]->format_name . ' が正常に削除されました');
    }

    public function editPaySlipFormat( Request $request )
    {
        $format_data = PaylistModel::get_document_approval_format(0,$request->id);
        if(!empty($format_data)){
            $format_data[0]->value = json_decode($format_data[0]->value);
        }
        $format_data = !empty($format_data) ? $format_data[0]: [];
        $is_default = (!empty($format_data) && $format_data->is_default == 1) ? true : false;
        return response()->json([
            'format_data' => $format_data,
            'is_default'  => $is_default
        ]);
    }
    
    public function getPaySlipFormatByGroup (Request $request) {
        $group_id = $request->group_id;
        $format_list = PaylistModel::get_document_approval_format($group_id,0);
        $response = "";
        $response = view('Staff.Paylist.Includes.item_payslip_format')->with('modal_format', $format_list);
        return $response;
    }

    public function getListPaySlipFormat (Request $req) {
        $format_list = PaylistModel::getListPaySlipFormat();
        $html = view('Staff.Paylist.Includes.item_payslip_format')->with('modal_format', $format_list)->render();
        return response()->json(['html' => $html]);
    }

    public function downloadPaySlipFormat( Request $request )
    {

        $format_data = PaylistModel::get_document_approval_format(0,$request->id);

       try {
          
            if(!empty($format_data)){
                $TEMP_PATH = 'storage/template/payslip_format.xlsx';
                $BEGIN_ROW = 1;
                $objPHPExcel = \PhpOffice\PhpSpreadsheet\IOFactory::load(
                    base_path($TEMP_PATH) /// return string path
                );
                $objPHPExcel->getActiveSheet(1)
                ->setCellValueByColumnAndRow(1, $BEGIN_ROW, "社員コード")
                ->setCellValueByColumnAndRow(2, $BEGIN_ROW, "アカウント番号")
                ->setCellValueByColumnAndRow(3, $BEGIN_ROW, "氏名")
                ->setCellValueByColumnAndRow(4, $BEGIN_ROW, "フォーマットID")
                ->setCellValueByColumnAndRow(5, $BEGIN_ROW, "所属")
                ->setCellValueByColumnAndRow(6, $BEGIN_ROW, "役職")
                ->setCellValueByColumnAndRow(7, $BEGIN_ROW, "出力対象給料")
                ->setCellValueByColumnAndRow(8, $BEGIN_ROW, "支給日")
                ->setCellValueByColumnAndRow(9, $BEGIN_ROW, "基本給区分");
                $header_data = json_decode($format_data[0]->value);
                foreach($header_data as $k => $v){
                    $objPHPExcel->getActiveSheet(1)
                    ->setCellValueByColumnAndRow((10 + $k), $BEGIN_ROW, $v->value);
                }
                $prefixFileName = "CSV".$format_data[0]->format_name."-";
                $date = date('Y_m_d');
                $filename = "$prefixFileName$date.csv";
    
                // Xóa trước cho chắc
                if (ob_get_level() > 0) {
                    ob_end_clean();
                }
    
                header('Content-Encoding: UTF-8');
                header("Content-Type: application/vnd.ms-excel");
                header("Content-Disposition: attachment; filename=\"$filename\"");
                $objWriter = new Csv($objPHPExcel);
                $objWriter->setUseBOM(true);
                ob_start();
                $objWriter->save("php://output");
                $xlsData = ob_get_contents();
                ob_end_clean();
                $res = [
                    'success' => true,
                    'message' => 'Export compete.',
                    'filename' => $filename,
                    'file' => "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; base64,".base64_encode($xlsData) //mime type of used format
                ];
                return Response::json($res, 200);
            }
        } catch (\Throwable $th) {
            return Response::json([
                'success' => false,
                'message' => 'Export fails.'
            ], 500);
        }
    }

    public function checkPayslipFormatUsed (Request $request) {
        $_id = $request->id;
        $check_Used = PaylistModel::checkPayslipFormatUsed($_id);
        
        $is_exist = 0;
        if(!empty($check_Used)){
            $is_exist = 1; 
        }
        
        return $is_exist;
    }

    function copyPayslipFormat(Request $req){
        $format_id = $req->format_id;
        try {
            $format = PaylistModel::getPayslipFormat($format_id);
            if($format != null){
                $sort  = $format->sort;
                $data_format = array(
                    'format_name' => $format->format_name."のコピー",
                    'creator_admin_id' => auth()->user()->admin_id,
                    'sort' =>  $sort+1,
                    'value' => $format->value,
                    'create_date' => date('Y-m-d H:i:s'),
                    'delete_date' => NULL
                );
                $new_format_id = DB::table('payslip_format_header')->insertGetId($data_format);
                // Sắp xếp lại thứ tự hiển thị
                $format_list = PaylistModel::getListPaySlipFormat();
                if(!empty($format_list)){
                    foreach($format_list as $val){
                        if($val->is_default == false && $val->id != $new_format_id && $val->id != $format_id){
                            $data_sort = array(
                                'sort' => $val->sort + 1
                            );
                            DB::table('payslip_format_header')->where('id', $val->id)->update($data_sort);
                        }
                    }
                }
            }
            $status = 'success'; 
            $msg  = 'データのコピーに成功';
        } catch(\Exception $e){
            $status = 'error'; 
            $msg = $e->getMessage();
        }
        return response()->json(['status' => $status,'msg' => $msg]);
    }
}